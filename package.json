{"name": "lastengineers", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.0.0", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@ng-select/ng-select": "^11.0.0", "@ngrx/effects": "^20.0.1", "@ngrx/store": "^20.0.1", "@ngrx/store-devtools": "^20.0.1", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "angular-google-charts": "^16.0.1", "angular2-draggable": "^16.0.0", "bootstrap": "^5.3.0", "ng-recaptcha": "^12.0.1", "rxjs": "~7.8.0", "three": "^0.153.0", "three-mesh-bvh": "^0.5.6", "threejs-slice-geometry-typescript": "^0.3.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.0.3", "@angular/cli": "~16.0.3", "@angular/compiler-cli": "^16.0.0", "@types/jasmine": "~4.3.0", "@types/three": "^0.152.1", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.0.2"}}