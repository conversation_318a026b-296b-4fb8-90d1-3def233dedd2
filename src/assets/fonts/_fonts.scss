@font-face {
    font-family: '<PERSON>';
    src: local('<PERSON>'), local('<PERSON>'),
        url('MullerRegular.otf') format('OpenType');
}

@font-face {
    font-family: 'Inter';
    src: local('Inter-Bold'), local('Inter-Bold'), url('inter/Inter-Bold.ttf') format('truetype');
}

@font-face {
    font-family: 'Inter';
    src: local('Inter-Regular'), local('Inter-Regular'), url('inter/Inter-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'MullerBold';
    src: local('<PERSON>'), local('<PERSON>'),
        url('MullerBold.otf') format('OpenType');
    font-weight: 700;
    font-style: normal;
}

// @font-face {
//     font-family: 'Muller';
//     src: local('<PERSON>'), local('Muller'),
//         url('MullerMedium.otf') format('OpenType');
//     font-weight: bold;
//     font-style: normal;
// }
// @font-face {
//     font-family: '<PERSON>';
//     src: local('<PERSON>'), local('Muller'),
//         url('MullerBold.otf') format('OpenType');
//     font-weight: bolder;
//     font-style: normal;
// }

// @font-face {
//     font-family: 'Muller';
//     src: local('Muller'), local('Muller'),
//         url('MullerUltraLight.otf') format('OpenType');
//     font-weight: lighter;
//     font-style: normal;
// }
