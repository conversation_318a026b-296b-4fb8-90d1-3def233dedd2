{"DATABASE": {"NAV_ITEM": "Database", "DATABASE": "Database", "INDIVIDUAL": "Individual", "FOOT_ID_CREATION_BTN": "Last ID Creation", "EMPTY_LIST_MESSAGE": "Last List is Empty", "UPLOAD_STL_MESSAGE": "Upload 3D STL Model", "CUSTOMIZATION_MESSAGE": "Customize Inventory", "DELETE_CONFIRMATION": "Are You Sure to delete this last"}, "FOOT_AVG": {"NAV_ITEM": "Last Averaging", "LIST_TITLE": "Average", "NEW": "Create Last Average", "ID": "Last Average ID", "VIEW": "Average Last View", "CREATION": "Average Last Creation", "FOOT_LIST": "Last List", "DELETE_CONFIRMATION": "Are You Sure to delete this last average"}, "DATA_ANALYTICS": {"NAV_ITEM": "Data Analytics", "BAR_CHART": "Bar Chart", "PIE_CHART": "Pie Chart", "LINE_CHART": "Line Chart", "MAP_CHART": "Map Chart", "NEW_CHART": "New Chart", "DELETE_CHART_CONFIRMATION": "Are You Sure to delete this chart ?", "DATA_VISUALISATION": "Data Visualisation"}, "FITTING": {"NAV_ITEM": "Fitting", "MATCHING": "Matching", "LIST_NAME": "Last Last", "OPTIMISATION_NAV_ITEM": "Optimisation", "FOOT_LAST_SELECT_MESSAGE": "Please select a Last / Last Matching", "SELECTED_FOOT": "Selected Last", "SELECTED_LAST": "Selected Last", "ADD_FOOT_BTN": "Add a Last", "ADD_LAST_BTN": "Add a Last", "EMPTY_LIST_MESSAGE": "Last Last List is Empty", "RECENT_FITTING": "Recent Matching", "METRICS": "Metrics", "LAST_METRICS": "Last Metrics", "FOOT_METRICS": "Last Metrics", "INITIAL_LAST_METRICS": "Initial Last Metrics", "INITIAL_SCORE": "Initial Fitting Score", "MODIFIED_LAST_METRICS": "Modified Last Metrics", "OPTIMIZED_LAST_METRICS": "Optimized Last Metrics", "FITTING_SCORE": "Fitting Score", "GENERATE_PDF": "Generate PDF", "DETAILS": "Details", "PLANE": "Plane", "MODULE": "<PERSON><PERSON><PERSON>", "SLICE": "Slice", "SLICING": "Slicing", "SLICES_LIST": "Slices List", "SLICES_PDF": "Slices PDF", "OBJECTS_POSITION": "Objects Position", "CAPTURE": "Capture", "NEW_CAPTURE": "New Capture", "LINE_WIDTH": "Line Width", "SWITCH": "Switch", "DOWNLOAD_INITIAL_LAST": "Download Initial Last"}, "FITTING_EVALUATION": {"NAV_ITEM": "Evaluation", "REPORT": "Evaluation Report", "EVALUATE": "Evaluate", "ANALYSED": "Analysed", "RE_EVALUATE": "Re-Evaluate", "SETTINGS": "Evaluation Setting", "DIFFERENCES": "Differences", "TOLERANCES": "Tolerances", "NEW_SETTINGS": "New Evaluation Settings", "NEW_SETTINGS_REF": "Please Your New Evaluation Settings Reference", "DELETE": "Delete Evaluation Settings", "DELETE_CONFIRMATION": "Deleting This Evaluation setting will delete all Last Last use this settings"}, "FITTING_MODIFICATION": {"NAV_ITEM": "Modification", "REPORT": "Modification Report", "MODIFY": "Modify", "MODIFIED": "Modified", "ADD_TO_LAST_INVENT": "Add to My last inventory", "DOWNLOAD": "Download Modified Last", "SCORE": "Modified Fitting Score"}, "FITTING_OPTIMISATION": {"NAV_ITEM": "Optimisation", "REPORT": "Optimisation Report", "OPTIMIZED": "Optimized", "DOWNLOAD": "Download Optimized Last", "SCORE": "Optimized Fitting Score"}, "FITTING_FOOT_INFO": {"NAV_ITEM": "Last Information", "SIZE_TYPE": "Size Type", "ESTIM_SIZE": "Estimated Size", "WIDE_WIDTH": "Wide Width", "STANDARD": "Standard"}, "TOOLBOX": {"NAV_ITEM": "Toolbox", "ALIGNMENT": "Alignment", "HEEL_ALIGNMENT": "Heel Alignement", "INIT_POSITION": "Initial position", "MODIFIED": "Modified", "OPTIMIZED": "Optimized", "TRANS": "Translation", "TRANS_ACTION": "Translate", "ROTAT": "Rotation", "ROTAT_ACTION": "Rotate", "INITIAL": "Initial", "ANIMATION": "Animation", "VERT_PLAN": "Vertical plan", "HORIZ_PLAN": "Horizontal plan"}, "HOME": {"HEADER": "Homepage", "ADMIN": "ADMIN", "ACCOUNT": "My Account", "ACTIVITY": "Activity", "DATABASE_STAT": "Individual Last", "FOOT_AVG_STAT": "Last Average", "CONNEXION_STAT": "Last Connexion", "MODULES": "<PERSON><PERSON><PERSON>", "SUPPORT": "Support"}, "COMMON": {"CREATE_BTN": "Create", "UPDATE_BTN": "Update", "SEARCH_BTN": "Search", "CLEAR_BTN": "Clear", "CLOSE_BTN": "Close", "CANCEL_BTN": "Cancel", "DELETE_BTN": "Delete", "DOWNLOAD_BTN": "Download", "START_BTN": "Start", "BACK_BTN": "Back", "VIEW_PDF_BTN": "View PDF", "NEW_BTN": "New", "SAVE_BTN": "Save", "OPEN": "Open", "LIST_ITEMS_TO_SHOW": "Number of items to show", "DATA_EXPORT_BTN": "Export Data in EXCEL", "MINIMUM": "Minimum", "MAXIMUM": "Maximum", "MIN": "Min", "MAX": "Max", "FOOT_ID": "Last ID", "FOOT": "Last", "LAST": "Last", "ALL": "ALL", "SELECT_FOOT": "Select Last", "YES": "Yes", "NO": "No", "MIXED": "Mixed", "MULTIPLE": "Multiple", "SUCCESS_DOWNLOAD": "Downloaded Successfully", "INFOS": "Infos", "AND": "And"}, "TABLE_HEADERS": {"COMPUTED_SIZE": "Computed Size", "SHOE_SIZE": "Shoe Size", "SHOE_TYPE": "Shoe Type", "STATUS": "Status", "ACTION": "Actions", "NOTE": "Note", "CLIENT_NOTE": "Client note", "SYSTEM_SIZE": "System Size", "MEASURES_GROUP": "Measures Group"}, "FILTERS": {"HEADER": "Filters", "NO_FILTERS": "No filters selected!", "MAX_HEIGHT": "Max Height", "MIN_HEIGHT": "Min Height", "MAX_WEIGHT": "Max Weight", "MIN_WEIGHT": "Min Weight", "MAX_SIZE": "<PERSON>", "MIN_SIZE": "<PERSON>", "MAX_AGE": "Max Age", "MIN_AGE": "Min Age"}, "FOOT_VIEW": {"HEADER": "Last View", "LEFT": "Left", "RIGHT": "Right"}, "PAGINATION": {"CONTENT": "Showing {{ first_el }} to {{ last_el }} of {{ all_el }} elements"}, "SCANNERS": {"NAV_ITEM": "Scanners", "PROPERTY": "My Scanners", "EMPTY_LIST_MESSAGE": "Scanners List is Empty", "ADD_SCANNER": "Add a Scanner", "NEW_SCANNER": "New Scanner", "DEVICE": "<PERSON><PERSON>", "NUMBER": "Scan Number", "TYPE": "Type", "NUMBER_OF_SCAN": "Number of Scan", "NAME_OF_SCAN": "Name of <PERSON><PERSON><PERSON>", "PLACEHOLDER": "Select Scanner", "SET_STATUS": "Enable/Disable", "DELETE_CONFIRM": "Are You Sure to delete this scanner ?", "CODE_TO_CONNECT": "Please use this code to connect {{selectedScannerName}} Device", "NEW_BLUE_SCAN": "a new Blue Scan"}, "USERS": {"NAV_ITEM": "Users", "EMPTY_LIST_MESSAGE": "Users List is Empty", "ADD_USER": "Add a User", "FIRST_NAME": "First Name", "LAST_NAME": "Last Name", "EMAIL": "Email", "LAST_CONNEXION": "Last Connexion", "LOG_IN": "Log In", "LOG_IN_AS_ADMIN": "<PERSON>g In as Ad<PERSON>", "LOG_OUT": "Log Out", "ACTIONS": "Users Actions", "PASSWORD": "Password", "CHANGE_PASSWORD": "Change Password", "ACTIVATION": "Activation", "RESET_ACTIVATION": "Reset Activation", "MANUAL_ACTIVATION": "Manual Activation", "CONNECT_TO_LAST": "Connect to Last Engineers", "DELETE": "Delete Account", "INFO": "User Info", "NEW": "New User"}, "STATUS": {"ENABLED": "Enabled", "DISABLED": "Disabled", "PROCESSING": "Processing", "ERROR": "Error"}, "IDENTIFICATION": {"NAME": "Name", "CITY": "City", "ROLE": "Role", "COUNTRY_PLACEHOLDER": "Select Country", "ADDRESS": "Address"}, "COMPANIES": {"NAV_ITEM": "Companies", "DATA_QUANTITY": "Data Quantity", "EMPTY_LIST_MESSAGE": "Companies List is Empty", "ADD_COMPANY": "Add a Company", "INFO": "Company Info", "NEW": "New Company", "USER_LIMIT": "User Limit", "PHONE": "Phone", "TVA": "TVA", "MAX_AF": "Max AF", "MAX_IF": "Max IF"}, "SYSTEM_ADMINISTRATION": {"NAV_ITEM": "System <br> Administration", "MANAGEMENT": "Management", "LAST_DAY": "Last 24 hours", "LAST_7_DAYS": "Last 7 days"}, "CUSTOM_FIELDS": {"NAV_ITEM": "Custom Fields", "ADD_FIELD": "Add Field", "NEW_FIELD": "New Field", "FIELD_TYPE": "Type of Field", "TEXT_TYPE": "Text", "NUMBER_TYPE": "Number", "CHOICE_TYPE": "Choice", "OPTION": "Option", "ADD_CHOICE": "Add Choice", "NAME": "Name of Field", "IS_REQUIRED": "Is Required", "IS_INTEGER": "Is Integer", "UNIT_NAME": "Unit Name", "RANGE": "Range", "MY_FIELDS": "My Fields"}, "FORM_SEARCH": {"TITLE": "Search Tool", "GENERAL": "General", "MEASURES_GROUP": "Measures"}, "BACKEND": {"You Reached Max Individual Last Inventory Limit": "You Reached Max Individual Last Inventory Limit", "You need to provide one last at least": "You need to provide one last at least", "Error on saving files": "Error on saving files", "Last ID Created Sucessfully": "Last ID Created Sucessfully", "Error on creating new Last": "Error on creating new Last", "Last not found": "Last not found", "Last ID updated Sucessfully": "Last ID updated Sucessfully", "Error on updating Last": "Error on updating Last", "Error on getting Lasts": "Error on getting Lasts", "Last deleted successfully.": "Last deleted successfully.", "Last Average": "Last Average", "You Reached Max Averaging Last Limit": "You Reached <PERSON> Averaging Last Limit", "You have to select at least 2 last": "You have to select at least 2 last", "Last Average Created Sucessfully": "Last Average Created Sucessfully", "Error on creating new Last Average": "Error on creating new Last Average", "Last Average not found": "Last Average not found", "Last Average Updated Sucessfully": "Last Average Updated Sucessfully", "Error on updating Last Average": "Error on updating Last Average", "Last Average deleted successfully": "Last Zverage deleted successfully", "Last Average not Found": "Last Average not Found", "Error on getting Last Averages": "Error on getting Last Averages", "Welcome to lastengineers": "Welcome to lastengineers", "Invalid JSON data": "Invalid JSON data", "Language name already exists, please select another name": "Language name already exists, please select another name", "No Language is Added yed!": "No Language is Added yed!", "Translation file does not found": "Translation file does not found", "Error on getting Scanners": "Error on getting Scanners", "Scanner name already exists, please choose another name!": "Scanner name already exists, please choose another name!", "Scanner Created Sucessfully": "Scanner Created Sucessfully", "Error on creating new Scanner": "Error on creating new Scanner", "Scanner deleted successfully": "Scanner deleted successfully", "Error on Deleting Scanner": "Error on Deleting Scanner", "Please verify your code": "Please verify your code", "Entered code is related to a disabled Scanner": "Entered code is related to a disabled Scanner", "First name most be provided": "First name most be provided", "Last name most be provided": "Last name most be provided", "This Email already exist": "This Email already exist", "Reference most be provided": "Reference most be provided", "This User Reference already exist": "This User Reference already exist", "Password most be provided": "Password most be provided", "Registered Successfully": "Registered Successfully", "Error on registering users": "Error on registering users", "Columns added successfully": "Columns added successfully", "Error on creating columns": "Error on creating columns", "Error on getting User infos": "Error on getting User infos", "Items number updated successfully": "Items number updated successfully", "Error on updating Items number": "Error on updating Items number", "Error on getting data": "Error on getting data", "Number of data": "Number of data", "User": "User", "Email": "Email", "Company": "Company", "Preview": "Preview", "Last ID": "Last ID", "Reference": "Reference", "Gender": "Gender", "Category": "Category", "Country": "Country", "Size": "Size", "L / R": "L / R", "Weight": "Weight", "Height": "Height", "Width": "<PERSON><PERSON><PERSON>", "Left Last Size": "Left Last Size", "Right Last Size": "Right Last Size", "Last Type": "Last Type", "Last Type (L/R)": "Last Type (L/R)", "Arch Type": "Arch Type", "Arch Type (L/R)": "Arch Type (L/R)", "Weight (kg)": "Weight (kg)", "Height (cm)": "Height (cm)", "Age": "Age", "Scanner": "Scanner", "Added Date": "Added Date", "Invented At": "Invented At", "Individuals (L/R)": "Individuals (L/R)", "Filters": "Filters", "Created Date": "Created Date", "Generated At": "Generated At", "Right Last Not Found": "Right Last Not Found", "Left Last Not Found": "Left Last Not Found", "last_ref_not_found": "Last %Lastref%  not found", "Error on generating your pdf": "Error on generating your pdf", "Error on connecting Last Engineers": "Error on connecting Last Engineers", "Error on creating evaluation": "Error on creating evaluation", "Evaluation Selected Not Found": "Evaluation Selected Not Found", "Last Last Not Found": "Last Last Not Found", "Error on optimizing": "Error on optimizing", "Error on launch Modification": "Error on launch Modification", "Intial File Not Found": "Intial File Not Found", "Optimized File Not Found": "Optimized File Not Found", "Evaluation created Successfully": "Evaluation created Successfully", "You Can't modify default Setting": "You Can't modify default Setting", "Evaluation updated Successfully": "Evaluation updated Successfully", "Evaluation Setting Not Found": "Evaluation Setting Not Found", "You Can't delete default Setting": "You Can't delete default Setting", "Evaluation deleted Successfully": "Evaluation deleted Successfully", "Error on Evaluation Operation": "Error on Evaluation Operation", "Error on creating": "Error on creating", "You are limited to 10 Custom Fields": "You are limited to 10 Custom Fields", "Field Already Exist": "Field Already Exist", "Field Created Successfully": "Field Created Successfully", "Field Deleted Successfully": "Field Deleted Successfully", "Error on getting Users": "Error on getting Users", "User not Found": "User not Found", "admin_user": "%userName% User is Admin now", "Users Limit is reached": "Users Limit is reached", "System size most be provided": "System size most be provided", "User Added Successfully": "User Added Successfully", "Email most be provided": "Email most be provided", "User Updated Successfully": "User Updated Successfully", "Connected Successfully": "Connected Successfully", "Male": "Male", "Female": "Female", "Kid": "<PERSON>", "Adult": "Adult", "Greek": "Greek", "Egyptian": "Egyptian", "Roman": "Roman", "German": "German", "Celtic": "Celtic", "Unknown": "Unknown", "Flat": "Flat", "Normal": "Normal", "High": "High", "L": "L", "R": "R", "right": "right", "left": "left", "error": "error", "processing": "processing", "processed": "processed", "new": "new", "Tracking Sheet": "Tracking Sheet", "Navicular Perimeter": "Navicular Perimeter", "Last Length": "Last Length", "Entry Perimeter": "Entry Perimeter", "Joint Perimeter": "Joint Perimeter", "Toe Perimeter": "Toe Perimeter", "Heel Width": "<PERSON><PERSON>", "Heel Width DL": "<PERSON><PERSON>", "Join Width": "Join <PERSON>", "Join Width DL": "Join <PERSON>", "Instep 1 Height": "Instep 1 Height", "Instep 2 Height": "Instep 2 Height", "Meta Height": "Meta Height", "Heel Height": "Heel Height", "Arch Height": "Arch Height", "Toe Height": "Toe Height", "Meta Ext Point": "Meta Ext Point", "Meta Int Point": "Meta Int Point", "Heel Ext Point": "Heel Ext Point", "Heel Int Point": "Heel Int Point", "Morpho Ground Point": "Morpho Ground Point", "Hallux Valgus Angle": "<PERSON><PERSON>", "Last Print": "Last Print", "Arch": "Arch", "Ground": "Ground", "Lengths (mm)": "Lengths (mm)", "Widths (mm)": "Widths (mm)", "Heights (mm)": "Heights (mm)", "Perimeters (mm)": "Perimeters (mm)", "Angles (°)": "Angles (°)", "Points (mm)": "Points (mm)", "Areas (mm²)": "Areas (mm²)", "Others": "Others", "years": "years"}}