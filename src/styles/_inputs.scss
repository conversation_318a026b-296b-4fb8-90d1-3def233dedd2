.dm-radio-input-v1 {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    input[type="radio"] {
        display: none;
    }

    input[type="radio"]+label {
        @include inline-flex-center;
        justify-content: flex-start;
        position: relative;
        background-color: $secondary;
        color: $dark;
        margin: 0.15rem 0.25rem;
        padding: 0.4rem 0.25rem 0.15rem 0.8rem;
        border-radius: 0.25rem;
        font-size: 0.8rem;
        min-width: 4.5rem;
        text-transform: capitalize;
        cursor: pointer;

        &::before {
            content: '';
            position: absolute;
            left: 0.4rem;
            height: 45%;
            width: 2px;
            border-radius: 10px;
            background-color: rgba($dark, 0.75);
            z-index: 2;
            transform: translateY(-50%);
            top: 50%;
        }
    }

    input[type="radio"]:checked+label {
        background-color: $primary;
        color: white;

        &::before {
            background-color: white;
        }
    }
}
.dm-checkbox-input-v1 {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    input[type="checkbox"] {
        display: none;
    }

    input[type="checkbox"]+label {
        @include inline-flex-center;
        justify-content: flex-start;
        position: relative;
        background-color: $secondary;
        color: $dark;
        margin: 0.15rem 0.25rem;
        padding: 0.4rem 0.25rem 0.15rem 0.8rem;
        border-radius: 0.25rem;
        font-size: 0.8rem;
        min-width: 4.5rem;
        text-transform: capitalize;
        cursor: pointer;

        &::before {
            content: '';
            position: absolute;
            left: 0.4rem;
            height: 45%;
            width: 2px;
            border-radius: 10px;
            background-color: rgba($dark, 0.75);
            z-index: 2;
            transform: translateY(-50%);
            top: 50%;
        }
    }

    input[type="checkbox"]:checked+label {
        background-color: $primary;
        color: white;

        &::before {
            background-color: white;
        }
    }
}
.dm-radio-input-v4 {
    @extend .dm-radio-input-v1;
    input[type="radio"]+label {
        min-width: unset;
        text-transform: unset;
        &::before {
            display: none;
        }
    }
}
.dm-checkbox-input-v4 {
    @extend .dm-checkbox-input-v1;
    input[type="checkbox"]+label {
        min-width: unset;
        text-transform: unset;
        &::before {
            display: none;
        }
    }
}

.dm-single-checkbox-input {
    display: flex;
    align-items: center;

    input[type="checkbox"] {
        position: relative;
        width: 3rem;
        height: 1.5rem;
        border-radius: 2.5rem;
        appearance: initial;
        background-color: rgba($dark, 0.2);
        transition: background-color 0.6s linear;
        outline: none;
        margin-right: 1rem;
    }

    input[type="checkbox"]:checked {
        background-color: $primary;
    }

    input[type="checkbox"]::before {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        content: "";
        font-weight: 700;
        top: 0.08rem;
        left: 0.1rem;
        width: 1.3rem;
        height: 1.3rem;
        border-radius: 2.5rem;
        transition: left 0.4s linear;
        background-color: white;
    }

    input[type="checkbox"]:checked::before {
        top: 0.08rem;
        left: 1.6rem;
        content: "";
    }
}

// .dm-radio-input-v2 {
//         padding: 0 0.5rem;
//         color: $primary;
//         input[type="radio"] {
//             display: none;
//         }
//         input[type="radio"]+label {
//             cursor: pointer;
//             border: 1px solid $primary;
//             border-right: 0;
//             font-size: 1rem;
//             text-align: center;
//             padding: 0.15rem;
//             &:last-child {
//                 border-right: 1px solid $primary;
//             }
//         }
//         input[type="radio"]:checked+label {
//             background-color: $primary;
//             color: white;
//         }
    
// }


.dm-radio-input-v3 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba($dark, 0.85);
        width: 60%;
        input[type="radio"] {
            display: none;
        }

        input[type="radio"]+label {
            flex: 1 1 auto;
            cursor: pointer;
            border: 1px solid rgba($dark, 0.85);
            border-right: 0;
            font-size: 1rem;
            text-align: center;
            padding: 0.15rem;
            text-transform: capitalize;
            min-width: 2.5rem;

            &:last-child {
                border-right: 1px solid rgba($dark, 0.85);
            }
        }
        input[type="radio"]:checked+label {
            background-color: rgba($dark, 0.85);
            color: white;
        }

}
.dm-checkbox-input-v3 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba($dark, 0.85);
        width: 60%;
        input[type="checkbox"] {
            display: none;
        }

        input[type="checkbox"]+label {
            flex: 1 1 auto;
            cursor: pointer;
            border: 1px solid rgba($dark, 0.85);
            border-right: 0;
            font-size: 1rem;
            text-align: center;
            padding: 0.15rem;
            text-transform: capitalize;
            min-width: 2.5rem;

            &:last-child {
                border-right: 1px solid rgba($dark, 0.85);
            }
        }
        input[type="checkbox"]:checked+label {
            background-color: rgba($dark, 0.85);
            color: white;
        }

}

input.dm-text-input{
    border: 0;
    outline: 0;
    width: 100%;
    padding: 0 0.5rem;
    font-size: 1rem;
    &:-webkit-autofill,
    &:-webkit-autofill:hover, 
    &:-webkit-autofill:focus, 
    &:-webkit-autofill:active{
        -webkit-background-clip: text;
        -webkit-text-fill-color: $dark;
        box-shadow: inset 0 0 20px 20px $white;
    }
}


.note{
    width: 100%;
    flex: 1 1 auto;
    border: 1px solid $secondary;
    border-radius: 5px;
    padding: 0.35rem 1rem;
    font-size: 1rem;
    &>p{
        border-bottom: 1px solid $secondary;
        width: 50%;
        font-size: 1rem;
    }
    textarea{
        width: 100%;
        height: 90px;
        padding: 0.5rem;
        max-height: 60px;
        resize: none;
        border: 0;
        border-radius: 5px;
        box-shadow: 0px 0px 5px rgba($dark, 0.15);
    }
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type=number] {
    -moz-appearance: textfield;
}

.dm-checkbox-input-select {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    
    input[type="checkbox"] {
        display: none;
    }
    input[type="checkbox"]+label {
        cursor: pointer;
        border: 1px solid rgba($dark, 0.75);
        border-radius: 0.5rem;
        width: 20px;
        height: 20px;
        background-color: white;
    }
    input[type="checkbox"]:checked+label {
        background-image: url('../assets/icons/checked-icon.svg');
        background-color: unset;
        border: 0;
        border-radius: 0;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    input[type="checkbox"]+label[semi-checked=true] {
        background-image: url('../assets/icons/semi-checked-icon.svg');
        background-color: unset;
        border: 0;
        border-radius: 0;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

}



