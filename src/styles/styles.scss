/* You can add global styles to this file, and also import other style files */
@import "./variables";
@import "bootstrap";
@import "./mixins";
@import "./buttons";
@import "./inputs";
@import "./badges";
@import "~@ng-select/ng-select/themes/default.theme.css";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-size: large;
  cursor: auto;

  a {
    text-decoration: none;
    cursor: pointer;

    img {
      cursor: pointer;
    }
  }

  button {
    text-decoration: none;
    cursor: pointer;
  }

  &:focus {
    outline: none;
    border: 0;
  }

  &::-webkit-scrollbar {
    height: 5px;
    width: 3px;
    background-color: $secondary;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $primary;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 1px rgba($primary, 0.3);
    background-color: $secondary;
  }
  @supports not selector(::-webkit-scrollbar) {
    * {
        scrollbar-color: $primary $secondary;
        scrollbar-width: thin;
        // user-select: none;
    }
  }
}

.pointer {
  cursor: pointer;

  * {
    cursor: pointer;
  }
}
@media (max-height: 700px) {
  html {
    font-size: 10px;
  }
}
@media (min-height: 700px) and (max-height: 800px) {
  html {
    font-size: 11px;
  }
}
@media (min-height: 800px) and (max-height: 900px) {
  html {
    font-size: 12px;
  }
}
@media (min-height: 900px) {
  html {
    font-size: 14px;
  }
}

body {
  @include flex-center;
  // width: 100vw;
  height: 100vh;
  background-color: rgba($dark, $alpha: 0.05);
  font-size: 1rem;
}

app-navigation,
app-scene,
app-main-scene,
app-second-scene,
app-inventory-listing,
app-avg-inventory-listing,
app-last-view {
  width: 100%;
  height: 100%;
  display: block;
}

app-last-view {
  display: block;
}

app-searchable-select,
app-field {
  width: 100%;
}

app-field {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.dm-shadow {
  box-shadow: 0px 0px 10px rgba($dark, 0.25);
}

.dm-absolute {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.uppercase {
  text-transform: uppercase;
}

.disabled {
  pointer-events: none;
}

.modal-container {
  display: block;
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba($dark, 0.15);
  z-index: 25;
  top: 0;
  left: 0;
}
.search-container {
  display: block;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 25;
  top: 0;
  left: 0;
}

.spinner {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: white;
  z-index: 21;

  .spinner-container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 10%;
    min-width: 80px;

    svg {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 40%;
      height: 40%;
      transform: translate(-50%, -50%);
    }

    .main {
      box-shadow: 0px 0px 10px rgba($primary, 0.25);
      position: relative;
      width: 100%;
      aspect-ratio: 1 / 1;
      // padding-top: calc(100% - 1rem);
      border: 0.5rem solid rgba($dark, 0.3);
      border-top: 0.5rem solid $primary;
      border-radius: 50%;
      animation: rotateAnimation 2s linear infinite;
    }
  }

  @keyframes rotateAnimation {
    0% {
      transform: rotate(-0deg);
    }

    100% {
      transform: rotate(-360deg);
    }
  }
}

.grid-container {
  width: 95vw;
  height: 95vh;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 4rem auto;
  // grid-template-rows: 65px calc(100% - 80px);
  gap: 15px;

  & {
    .navbar {
      border-radius: 0.7rem;
      width: 100%;
      height: 100%;
      background-color: white;
      padding: 0;
    }

    .body {
      border-radius: 0.7rem;
      width: 100%;
      height: 100%;
      display: grid;
      grid-template-columns: 64% 1% 35%;
      // gap: 1%;

      &>* {
        background-color: white;
        border-radius: 0.7rem;
        width: 100%;
        height: calc(95vh - 80px);
        padding: 0.5rem;
        overflow: auto;
      }
      &>*:nth-child(2){
        background-color: unset;
      }
    }

    .full-body {
      position: relative;
      border-radius: 0.7rem;
      width: 100%;
      height: calc(95vh - 80px);
      display: grid;
      grid-template-columns: 100%;
      gap: 1%;
      background-color: white;
      border-radius: 0.7rem;

    }
  }
}
@media (max-height: 850px) {
  .grid-container {
    height: 98vh;
  }
}
router-outlet{
  position: absolute !important;
  width: 0 !important;
  height: 0 !important;
}

// .search-container .search-slider {
  
// }

.search-slider {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
}
.overflow-y-auto{
  overflow-y: auto;
}
.btn-no-border, .btn-no-border:focus, .btn-no-border:active:focus {
    outline: none !important;
    border: 0;
}

.chart-title, .chart-title input {
  font-family: Inter;
  font-size: 15px;
  font-weight: bold;
  color: #6F6F6F;
  line-height: 18.15px;
  letter-spacing: .1em;
  border-bottom: 1px solid #DDE3E9;
  padding: 11px 0;
}

.h-separator {
  position: relative;
  height: 1px;
  background-color: rgba($color: $dark, $alpha: .2);

  img {
    position: absolute;
    left: 50%;
    bottom: 50%;
    transform: translate(-50%, 50%);
    cursor: pointer;
  }
}

.v-separator {
  position: absolute;
  left: 74px;
  top: 71px;
  width: 1px;
  height: 170px;
  background-color: $dark;
}

.legend-top-border {
  right: 0;
  bottom: 0;
}
.font-1{
  font-size: 1rem;
}


.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 25px;
    
  }
  .switch input { 
    opacity: 0;
    width: 0;
    height: 0;
  }
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    top: 2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
   
  }
  
  input:checked + .slider {
    background-color: $primary !important;
  }
  
  input:focus + .slider {
    box-shadow: 0 0 1px $primary;
  }
  
  input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
  }
  
  /* Rounded sliders */
  .slider.round {
    border-radius: 34px;
  }
  
  .slider.round:before {
    border-radius: 50%;
  }    


  .cut-border-bottom {
    position: relative;
}

.cut-border-bottom::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 10%;
    right: 10%;
    height: 0.07rem;
    top: 1rem;
    background-color: rgba(94, 94, 94, 0.2);

}

