/* You can add global styles to this file, and also import other style files */
@import '../assets/fonts/fonts';

$primary: #BB9233;
$secondary: #E9E9EA;
$success: #0A7447;
$info: #17a2b8;
$warning: #fa5919;
$danger: #ff4136;
$light: #faf7ef;
$dark: #212529;
$white: #ffffff;
$back: #f3f3f4;

$min-contrast-ratio:   2.5;

$color-contrast-dark:      $dark;
$color-contrast-light:     $light;

$font-family-sans-serif: 'Muller', system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
$font-family-monospace:  'Muller', SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

$theme-colors: (
  "primary":    $primary,
  "secondary":  $secondary,
  "success":    $success,
  "info":       $info,
  "warning":    $warning,
  "danger":     $danger,
  "light":      $light,
  "dark":       $dark
);

$sizes: 5,7,10, 15, 20, 25, 30, 33 ,35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 87, 88, 90, 95, 100;
$small_sizes: 0,1,2,3,4,5,6,7,8,9,10,12,15,20,24,30,35,40;