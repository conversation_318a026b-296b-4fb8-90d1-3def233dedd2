import { Injectable } from '@angular/core';
import {HttpClient, HttpParams} from "@angular/common/http";
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from 'src/app/services/user.service';
import { SortService } from 'src/app/services/sort.service';

@Injectable({
  providedIn: 'root'
})
export class StandardService {
  
  constructor(private http: HttpClient,
              private userService: UserService,
              private TranslateService: TranslateService,
              private sortService: SortService) { }

  createNewMode(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/modes/create', data);
  }

  getShoetypeInfosByRef(ref:string){
    return this.http.get(environment.api_url + 'api/'+ this.lang +'/admin/shoetypes/by-ref/' + ref);
  }
  
  toggleActiveStatus(ref:string, entity:string){
    return this.http.patch(environment.api_url + 'api/'+ this.lang +'/admin/' + entity + '/' + ref + '/toggle-active',{});
  }



  // updateLast(ref: any, data:any){
  //   return this.http.post(environment.api_url + 'api/'+ this.lang +'/lasts/update/' + ref, data);
  // }
  // getLastsByPage(page: number = 0){
  //   const pageSize : any = localStorage.getItem('pageSize') ? localStorage.getItem('pageSize') : '15';

  //   return this.http.get(environment.api_url + 'api/'+ this.lang +'/lasts/all',{
  //     params: new HttpParams().set('pageSize', pageSize).set('page', page)
  //   });
  // } 
  // deleteLast(ref: any) {
  //   return this.http.delete(environment.api_url + 'api/' + this.lang + '/lasts/' + ref);
  // }
  // getOneLast(ref: any) {
  //   return this.http.get(environment.api_url + 'api/' + this.lang + '/lasts/' + ref);
  // }
  // getSearchByPage(){

  //   let httpParams = new HttpParams();

  //   httpParams.append('limit', this.userService.user.parameters.pagination_size);

  //   return this.http.get(environment.api_url + 'api/'+ this.lang +'/lasts/list', { params : httpParams});
  // }
  get lang(){
    return this.TranslateService.getDefaultLang() ?? 'en'
  }
}
