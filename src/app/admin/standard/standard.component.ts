import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { AdminService } from '../services/admin.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { StaticDataService } from 'src/app/services/static.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { StandardService } from './standard.service';

@Component({
  selector: 'app-standard',
  templateUrl: './standard.component.html',
  styleUrls: ['./standard.scss']
})
export class StandardComponent implements OnInit, OnDestroy {
  subscriptions: Subscription[] = []
  companies: any[] = []
  company_users: any[] = []
  standards: any[] = []
  standards_to_show: any[] = this.standards
  open_popup: boolean = false;
  showActions: boolean = false;
  isLoading: boolean = false;
  selected_standard: any = null;
  addStandardForm: FormGroup;
  selectedStandardForm: FormGroup;
  searchText: string = "";


  constructor(public AdminService: AdminService,
    public StaticDataService: StaticDataService,
    private messageService: MessagesService,
    private StandardService: StandardService,
    private SpinnerService: SpinnerService,
  ) {
  }
  ngOnInit(): void {
    this.SpinnerService.show();
    this.addStandardForm = new FormGroup({
      name: new FormControl(null, Validators.required),
      company: new FormControl(null, Validators.required),
      admin: new FormControl(null, Validators.required)
    });  
    this.getAllCompanies$();
  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }
  showAddStandard() {
    this.open_popup = true;
  }
  showInfos(company: any) {
    this.open_popup = true;
    // this.selected_company = company
    // this.addStandardForm.patchValue({
    //   ref: company.ref,
    //   name: company.name,
    //   city: company.city,
    //   country: company.country['slug'],
    //   siret: company.siret,
    //   tva: company.tva,
    //   phone: company.phone,
    //   users_limit: company.users_limit,
    //   max_if: company.max_if,
    //   max_af: company.max_af
    // });
  }

  cancel() {
    this.open_popup = false;
    this.addStandardForm.reset()
  }
  onSubmitStandard() {
    if (this.selected_standard) {
      this.onEditStandard()
    } else {
      if (this.addStandardForm.valid) {
        this.isLoading = true
        // console.log(this.addStandardForm.value)
        this.subscriptions.push(this.AdminService.postNewStandard(this.addStandardForm.value).subscribe({
          next: (value) => {
            this.isLoading = false
            this.cancel();
            this.ngOnInit();
          },
          error: (err) => {
            this.isLoading = false
          },
        }))
      }
    }
  }

  onEditStandard() {
    this.isLoading = true
    this.subscriptions.push(this.AdminService.postUpdateStandard({ref:this.addStandardForm.get('ref')?.value, data: this.addStandardForm.value}).subscribe({
      next: (value) => {
        this.isLoading = false
        this.cancel();
        this.messageService.set("Standard Info updated Successfully")
        this.ngOnInit()
      },
      error: (err) => {
        console.log(err)
        this.isLoading = false
      }
    }))
  }

  select_company($event: any) {
    this.addStandardForm.controls['company'].setValue($event);
    this.getCompanyUsers$($event)
  }
  select_user($event: any) {
    this.addStandardForm.controls['admin'].setValue($event);
  }

  deleteStandard(id:number){
    this.SpinnerService.show();
    this.subscriptions.push(this.AdminService.deleteStandardById(id).subscribe((response: any) => {
      this.standards = response.data;
      this.standards_to_show = [...this.standards]
      this.searchText = ""
      this.SpinnerService.hide()
    }, (error) => {
      this.messageService.set('Error on deleting Standard', 'error')
      this.SpinnerService.hide()
    }))
  }

  search() {
    if (this.searchText.trim() != "") {
      this.standards_to_show = this.standards.filter((company) => company.name.toLowerCase().includes(this.searchText.toLowerCase()));
    } else {
      this.standards_to_show = [...this.standards]
    }
  }
  showActionsModal(company: any) {
    this.showActions = true;
    this.selected_standard = company;
  }

  hideActionsModal() {
    this.showActions = false;
    this.selected_standard = null;
  }

  getAllCompanies$() {
    this.SpinnerService.show();
    this.subscriptions.push(this.AdminService.getAllCompanies().subscribe((response: any) => {
      this.companies = response.data;
      this.searchText = ""
      this.getAllStandards$();
    }, (error) => {
      this.companies = []
      this.searchText = ""
      this.SpinnerService.hide()
    }))
  }
  getCompanyUsers$(id:string){
    this.SpinnerService.show();
    this.subscriptions.push(this.AdminService.getCompanyUsers(id).subscribe((response: any) => {
      this.company_users = response.data.users;
      this.searchText = ""
      this.SpinnerService.hide()
    }, (error) => {
      this.company_users = []
      this.searchText = ""
      this.SpinnerService.hide()
    }))
  }
  getAllStandards$() {
    this.subscriptions.push(this.AdminService.getAllStandards().subscribe((response: any) => {
      this.standards = response.data;
      this.standards_to_show = [...this.standards]
      this.searchText = ""
      this.SpinnerService.hide()
    }, (error) => {
      this.standards = []
      this.standards_to_show = [...this.standards]
      this.searchText = ""
      this.SpinnerService.hide()
    }))
  }
  toggleStatus(standard: any) {
    this.SpinnerService.show()
    this.subscriptions.push(this.StandardService.toggleActiveStatus( standard.ref , 'standards').subscribe((response: any) => {
      this.messageService.set(response.message, 'green')
      this.SpinnerService.hide()
    }, (error) => {
      standard.is_active = !standard.is_active 
      this.SpinnerService.hide()
    }))
  }
}