import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { AdminService } from '../../services/admin.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { StaticDataService } from 'src/app/services/static.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { ActivatedRoute } from '@angular/router';
import { StandardService } from '../standard.service';

@Component({
  selector: 'app-shoetype',
  templateUrl: './shoetype.component.html',
  styleUrls: ['../standard.scss']
})
export class ShoetypeComponent implements OnInit, OnDestroy {
  subscriptions: Subscription[] = []
  companies: any[] = []
  company_users: any[] = []
  shoetypes: any[] = []
  shoetypes_to_show: any[] = this.shoetypes
  open_popup: boolean = false;
  showActions: boolean = false;
  isLoading: boolean = false;
  selected_shoetype: any = null;
  addShoetypeForm: FormGroup;
  selectedShoetypeForm: FormGroup;
  searchText: string = "";
  standard_ref: string = ""


  constructor(public AdminService: AdminService,
    public StaticDataService: StaticDataService,
    private messageService: MessagesService,
    private SpinnerService: SpinnerService,
    private StandardService: StandardService,
    private route: ActivatedRoute
  ) {
  }
  ngOnInit(): void {
    this.SpinnerService.show();
    this.addShoetypeForm = new FormGroup({
      name: new FormControl(null, Validators.required),
      gender: new FormControl(null, Validators.required),
      category: new FormControl(null, Validators.required)
    });
    this.route.paramMap.subscribe(params => {
      this.standard_ref = params.get('standard_ref')!;
      this.getShoetypesByStandardRef$(this.standard_ref);
    });

  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }
  showAddShoetype() {
    this.open_popup = true;
  }
  showInfos(company: any) {
    this.open_popup = true;
    // this.selected_company = company
    // this.addShoetypeForm.patchValue({
    //   ref: company.ref,
    //   name: company.name,
    //   city: company.city,
    //   country: company.country['slug'],
    //   siret: company.siret,
    //   tva: company.tva,
    //   phone: company.phone,
    //   users_limit: company.users_limit,
    //   max_if: company.max_if,
    //   max_af: company.max_af
    // });
  }

  cancel() {
    this.open_popup = false;
    this.addShoetypeForm.reset()
  }
  onSubmitShoetype() {
    if (this.addShoetypeForm.valid) {
      this.isLoading = true
      this.subscriptions.push(this.AdminService.postNewShoetype({ ...this.addShoetypeForm.value, standard_ref: this.standard_ref }).subscribe({
        next: (value) => {
          this.isLoading = false
          this.cancel();
          this.ngOnInit();
        },
        error: (err) => {
          this.isLoading = false
        },
      }))
    }
  }

  onEditShoetype() {
    this.isLoading = true
    this.subscriptions.push(this.AdminService.postUpdateShoetype({ ref: this.addShoetypeForm.get('ref')?.value, data: this.addShoetypeForm.value }).subscribe({
      next: (value) => {
        this.isLoading = false
        this.cancel();
        this.messageService.set("Shoetype Info updated Successfully")
        this.ngOnInit()
      },
      error: (err) => {
        console.log(err)
        this.isLoading = false
      }
    }))
  }

  select_gender($event: any) {
    this.addShoetypeForm.controls['gender'].setValue($event);
  }
  select_category($event: any) {
    this.addShoetypeForm.controls['category'].setValue($event);
  }

  deleteShoetype(id: number) {
    this.SpinnerService.show();
    this.subscriptions.push(this.AdminService.deleteShoetypeById(id).subscribe((response: any) => {
      this.shoetypes = response.data;
      this.shoetypes_to_show = [...this.shoetypes]
      this.searchText = ""
      this.SpinnerService.hide()
    }, (error) => {
      this.messageService.set('Error on deleting Shoetype', 'error')
      this.SpinnerService.hide()
    }))
  }

  search() {
    if (this.searchText.trim() != "") {
      this.shoetypes_to_show = this.shoetypes.filter((company) => company.name.toLowerCase().includes(this.searchText.toLowerCase()));
    } else {
      this.shoetypes_to_show = [...this.shoetypes]
    }
  }
  showActionsModal(company: any) {
    this.showActions = true;
    this.selected_shoetype = company;
  }

  hideActionsModal() {
    this.showActions = false;
    this.selected_shoetype = null;
  }
  getShoetypesByStandardRef$(ref: string) {
    this.subscriptions.push(this.AdminService.getShoetypesByStandardRef(ref).subscribe((response: any) => {
      this.shoetypes = response.data;
      this.shoetypes_to_show = [...this.shoetypes]
      this.searchText = ""
      this.SpinnerService.hide()
    }, (error) => {
      this.shoetypes = []
      this.shoetypes_to_show = [...this.shoetypes]
      this.searchText = ""
      this.SpinnerService.hide()
    }))
  }
  toggleStatus(shoetype: any) {
    this.SpinnerService.show()
    this.subscriptions.push(this.StandardService.toggleActiveStatus(shoetype.ref, 'shoetypes').subscribe({
      next: (response: any) => {
        this.messageService.set(response.message, 'green')
        this.SpinnerService.hide()
      }, error: (error) => {
        shoetype.is_active = !shoetype.is_active 
        this.SpinnerService.hide()
      }
    }))
  }
}