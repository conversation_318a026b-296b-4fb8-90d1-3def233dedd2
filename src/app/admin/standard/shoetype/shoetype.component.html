<div class="full-body">
    <div class="w-100 h-100 d-flex flex-column p-2 admin-container overflow-y-auto">
        <app-spinner></app-spinner>
        <div class="header">
            <button class="btn-back" [routerLink]="['/standards']"> <img src="assets/icons/arrow-to-left.svg"
                    alt="back button">{{ 'Back' | translate }}</button>
            <div class="d-flex align-items-center title">
                <img src="assets/icons/company-icon.svg" alt="Company Icon">
                <p class="m-0 pt-1">{{ 'Shoetypes' | translate }}</p>
            </div>
            <div class="search">
                <input type="search" [placeholder]="'Search' | translate" (search)="search()" (keyup)="search()"
                    [(ngModel)]="searchText">
            </div>
        </div>
        <div class="flex-grow-1 d-flex flex-column mx-auto w-100 justify-content-start">
            <div class="flex-grow-1 table">
                <table class="table listing-table">
                    <thead class="table-dark sticky">
                        <tr>
                            <th scope="col">{{ 'Name' | translate }}</th>
                            <th scope="col">{{ 'Slug' | translate }}</th>
                            <th scope="col">{{ 'Gender' | translate }}</th>
                            <th scope="col">{{ 'Category' | translate }}</th>
                            <th scope="col">{{ 'Creation' | translate }}</th>
                            <th scope="col">{{ 'Status' | translate }}</th>
                            <th scope="col">{{ 'Actions' | translate}}</th>
                        </tr>
                    </thead>

                    <tbody *ngIf="shoetypes_to_show.length > 0">
                        <tr *ngFor="let shoetype of shoetypes_to_show">
                            <td>
                                <span>{{shoetype.name | empty}}</span>
                            </td>
                            <td>
                                <span>{{shoetype.slug | empty}}</span>
                            </td>
                            <td>
                                <span>{{shoetype.gender.name}}</span>
                            </td>
                            <td>
                                <span>{{shoetype.category.name }}</span>
                            </td>
                            <td>
                                <span>{{shoetype.created_at }}</span>
                            </td>
                            <td>
                                <label class="switch">
                                    <input type="checkbox" [(ngModel)]="shoetype.is_active"
                                        (change)="toggleStatus(shoetype)">
                                    <span class="slider round"></span>
                                </label>
                            </td>
                            <td>
                                <button class="mini-btn-gray btn-primary py-0 m-1">
                                    {{ 'Infos' | translate}}
                                </button>
                                <button class="mini-btn-gray btn-primary py-0 m-1"
                                    [routerLink]="['/standard', standard_ref ,'shoetype', shoetype.ref , 'modes']">
                                    {{ 'Modes' | translate }}
                                </button>
                                <br>
                                <button (click)="deleteShoetype(shoetype.id)"
                                    class="mini-btn-primary btn-primary py-0 m-1">
                                    {{ 'Delete' | translate }}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <h1 class="w-100 text-center pt-3" *ngIf="!(shoetypes_to_show.length > 0)">{{ 'Empty List' | translate
                    }}</h1>
            </div>
            <div class="w-95 mx-auto p-2">
                <button class="btn btn-primary px-3 rounded-40" (click)="showAddShoetype()">
                    <img class="p-1 pe-2" src="assets/icons/plus-icon.svg" alt="plus icon">
                    {{ 'Add Shoetype' | translate }}
                </button>
            </div>

        </div>
    </div>
</div>

<app-popup-ui (cancel)="cancel()" *ngIf="open_popup" class="w-50">
    <h4 title class="text-center text-primary">
        {{ 'Add Shoetype' | translate }}
    </h4>
    <form message class="row pe-4" (ngSubmit)="onSubmitShoetype()" [formGroup]="addShoetypeForm" autocomplete="off"
        style="width: 50vw; position: relative;">
        <div class="col w-100" style="min-height: 25vh;">
            <app-field name="Shoetype Name" [required]="true">
                <input autocomplete="off" formControlName="name" class="dm-text-input" type="text" name="name"
                    id="name">
            </app-field>
            <app-searchable-select name="Category" [required]="true" [list]="StaticDataService.categories"
                [selected_item]="''" (select_event)="select_category($event)"></app-searchable-select>

            <app-searchable-select name="Gender" [required]="true" [list]="StaticDataService.genders"
                [selected_item]="''" (select_event)="select_gender($event)"></app-searchable-select>
        </div>
        <div class="d-flex justify-content-center mt-5">
            <button class="btn btn-primary px-4" [disabled]="addShoetypeForm.invalid" type="button"
                (click)="onSubmitShoetype()">
                <img src="assets/icons/check-icon.svg" alt="close icon" width="16px" class="me-2">
                {{ 'Add Shoetype' | translate }}
            </button>
        </div>
    </form>
    <app-spinner-html spinner *ngIf="isLoading"></app-spinner-html>
</app-popup-ui>
<!-- 
<app-popup-ui (cancel)="hideActionsModal()" *ngIf="showActions" class="w-60">
    <h4 title class="text-center text-primary">
        {{ "Assign languages" | translate }}
    </h4>
    <form message class="row pe-4" style="min-height: 200px">
        <app-searchable-select 
            [placeholder]="'Select Languages' | translate" name="Languages" 
            [list]="languages" 
            [selected_list]="selectedLanguages"
            (select_event)="selectLanguages($event)"
            [isMultiple]="true">
        </app-searchable-select>
        <div class="d-flex justify-content-center mt-5 position-relative">
            <button class="btn btn-primary px-4 position-absolute b-0" type="button" (click)="assignLanguages()">
                {{ "COMMON.SAVE_BTN" | translate }}
            </button>
        </div>
    </form>
    <app-spinner-html spinner *ngIf="isAssigning"></app-spinner-html>
</app-popup-ui> -->