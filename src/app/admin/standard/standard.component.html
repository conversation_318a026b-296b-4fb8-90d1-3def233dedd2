<div class="full-body">
    <div class="w-100 h-100 d-flex flex-column p-2 admin-container overflow-y-auto">
        <app-spinner></app-spinner>
        <div class="header">
            <button class="btn-back" [routerLink]="['/admin']"> <img src="assets/icons/arrow-to-left.svg"
                    alt="back button">{{ 'Back' | translate }}</button>
            <div class="d-flex align-items-center title">
                <img src="assets/icons/company-icon.svg" alt="Company Icon">
                <p class="m-0 pt-1">{{ 'Standards' | translate }}</p>
            </div>
            <div class="search">
                <input type="search" [placeholder]="'Search' | translate" (search)="search()" (keyup)="search()" [(ngModel)]="searchText">
            </div>
        </div>
        <div class="flex-grow-1 d-flex flex-column mx-auto w-100 justify-content-start">
            <div class="flex-grow-1 table">
                <table class="table listing-table">
                    <thead class="table-dark sticky">
                        <tr>
                            <th scope="col">{{ 'Reference' | translate }}</th>
                            <th scope="col">{{ 'Administrator' | translate }}</th>
                            <th scope="col">{{ 'Company' | translate }}</th>
                            <th scope="col">{{ 'Status' | translate }}</th>
                            <th scope="col">{{ 'Actions' | translate}}</th>
                        </tr>
                    </thead>

                    <tbody *ngIf="standards_to_show.length > 0">
                        <tr *ngFor="let standard of standards_to_show">
                            <td>
                                <span>{{standard.name | empty}}</span>
                            </td>
                            <td>
                                <span>{{standard.admin.email}}</span>
                            </td>
                            <td>
                                <span>{{standard.company.name }}</span>
                            </td>
                            <td>
                                <label class="switch">
                                        <input type="checkbox" [(ngModel)]="standard.is_active" (change)="toggleStatus(standard)">
                                        <span class="slider round"></span>
                                </label>
                            </td>
                            <td>
                                <button class="mini-btn-gray btn-primary py-0 m-1">
                                    {{ 'Infos' | translate}}
                                </button>
                                <button class="mini-btn-gray btn-primary py-0 m-1" [routerLink]="['/standard', standard.ref, 'shoetypes']">
                                    {{ 'Shoe types' | translate }}
                                </button>
                                <br>
                                <button (click)="deleteStandard(standard.id)" class="mini-btn-primary btn-primary py-0 m-1">
                                    {{ 'Delete' | translate }}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <h1 class="w-100 text-center pt-3" *ngIf="!(standards_to_show.length > 0)">{{ 'Empty List' | translate }}</h1>
            </div>
            <div class="w-95 mx-auto p-2">
                <button class="btn btn-primary px-3 rounded-40" (click)="showAddStandard()">
                    <img class="p-1 pe-2" src="assets/icons/plus-icon.svg" alt="plus icon">
                    {{ 'Add Standard' | translate }}
                </button>
            </div>

        </div>
    </div>
</div>

<app-popup-ui (cancel)="cancel()" *ngIf="open_popup" class="w-50">
    <h4 title class="text-center text-primary">
        {{ 'Add Standard' | translate }}
    </h4>
    <form message class="row pe-4" (ngSubmit)="onSubmitStandard()" [formGroup]="addStandardForm" autocomplete="off" style="width: 50vw; position: relative;">
        <div class="col w-100" style="min-height: 25vh;">
            <app-field name="Standard Name" [required]="true">
                <input autocomplete="off" formControlName="name" class="dm-text-input" type="text"
                    name="name" id="name">
            </app-field>
            <app-searchable-select name="Company" [required]="true" [slug]="'id'" [list]="companies" 
            [selected_item]="''"
                (select_event)="select_company($event)"></app-searchable-select>

            <app-searchable-select name="Admin" display_name="email" [required]="true" [slug]="'id'" [list]="company_users" 
            [selected_item]="''"
                (select_event)="select_user($event)"></app-searchable-select>
        </div>
        <div class="d-flex justify-content-center mt-5">
            <button class="btn btn-primary px-4" [disabled]="addStandardForm.invalid" type="button" (click)="onSubmitStandard()">
              <img src="assets/icons/check-icon.svg" alt="close icon" width="16px" class="me-2">
              {{ 'Add Standard' | translate }}
            </button>
          </div>
    </form>
    <app-spinner-html spinner *ngIf="isLoading"></app-spinner-html>
</app-popup-ui>
<!-- 
<app-popup-ui (cancel)="hideActionsModal()" *ngIf="showActions" class="w-60">
    <h4 title class="text-center text-primary">
        {{ "Assign languages" | translate }}
    </h4>
    <form message class="row pe-4" style="min-height: 200px">
        <app-searchable-select 
            [placeholder]="'Select Languages' | translate" name="Languages" 
            [list]="languages" 
            [selected_list]="selectedLanguages"
            (select_event)="selectLanguages($event)"
            [isMultiple]="true">
        </app-searchable-select>
        <div class="d-flex justify-content-center mt-5 position-relative">
            <button class="btn btn-primary px-4 position-absolute b-0" type="button" (click)="assignLanguages()">
                {{ "COMMON.SAVE_BTN" | translate }}
            </button>
        </div>
    </form>
    <app-spinner-html spinner *ngIf="isAssigning"></app-spinner-html>
</app-popup-ui> -->
