import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { AdminService } from '../../services/admin.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { StaticDataService } from 'src/app/services/static.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { ActivatedRoute } from '@angular/router';
import { StandardService } from '../standard.service';

@Component({
  selector: 'app-admin-mode',
  templateUrl: './mode.component.html',
  styleUrls: ['../standard.scss']
})
export class ModeComponent implements OnInit, OnDestroy {
  subscriptions: Subscription[] = []
  companies: any[] = []
  company_users: any[] = []
  modes: any[] = []
  modes_to_show: any[] = this.modes
  open_popup: boolean = false;
  showActions: boolean = false;
  isLoading: boolean = false;
  selected_mode: any = null;
  addModeForm: FormGroup;
  selectedModeForm: FormGroup;
  searchText: string = "";
  shoetype_ref : string = ""
  standard_ref : string = ""


  constructor(public AdminService: AdminService,
    public StaticDataService: StaticDataService,
    private messageService: MessagesService,
    private SpinnerService: SpinnerService,
    private StandardService: StandardService,
    private route: ActivatedRoute
  ) {
  }
  ngOnInit(): void {
    this.addModeForm = new FormGroup({
      name: new FormControl(null, Validators.required),
      gender: new FormControl(null, Validators.required),
      category: new FormControl(null, Validators.required)
    });
    this.route.paramMap.subscribe(params => {
      this.shoetype_ref = params.get('shoetype_ref')!;
      this.standard_ref = params.get('standard_ref')!;
      this.getModesByShoetypeRef$(this.shoetype_ref);
    });
  
  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }
  showAddMode() {
    this.open_popup = true;
  }
  showInfos(company: any) {
    this.open_popup = true;
    // this.selected_company = company
    // this.addModeForm.patchValue({
    //   ref: company.ref,
    //   name: company.name,
    //   city: company.city,
    //   country: company.country['slug'],
    //   siret: company.siret,
    //   tva: company.tva,
    //   phone: company.phone,
    //   users_limit: company.users_limit,
    //   max_if: company.max_if,
    //   max_af: company.max_af
    // });
  }

  cancel() {
    this.open_popup = false;
    this.addModeForm.reset()
  }
  onSubmitMode() {
    if (this.selected_mode) {
      this.onEditMode()
    } else {
      if (this.addModeForm.valid) {
        this.isLoading = true
        this.subscriptions.push(this.AdminService.postNewMode({...this.addModeForm.value, shoetype_ref : this.shoetype_ref}).subscribe({
          next: (value) => {
            this.isLoading = false
            this.cancel();
            this.ngOnInit();
          },
          error: (err) => {
            this.isLoading = false
          },
        }))
      }
    }
  }

  onEditMode() {
    this.isLoading = true
    this.subscriptions.push(this.AdminService.postUpdateMode({ref:this.addModeForm.get('ref')?.value, data: this.addModeForm.value}).subscribe({
      next: (value) => {
        this.isLoading = false
        this.cancel();
        this.messageService.set("Mode Info updated Successfully")
        this.ngOnInit()
      },
      error: (err) => {
        console.log(err)
        this.isLoading = false
      }
    }))
  }

  select_gender($event: any) {
    this.addModeForm.controls['gender'].setValue($event);
  }
  select_category($event: any) {
    this.addModeForm.controls['category'].setValue($event);
  }

  deleteMode(id:number){
  this.SpinnerService.globalSpinnerSubject.next(true);
    this.SpinnerService.show();
    this.subscriptions.push(this.AdminService.deleteModeById(id).subscribe((response: any) => {
      this.messageService.set(response.message, 'green')
      this.modes = response.data;
      this.modes_to_show = [...this.modes]
      this.searchText = ""
      this.SpinnerService.hide()
      this.SpinnerService.globalSpinnerSubject.next(false);
    }, (error) => {
      this.messageService.set('Error on deleting Mode', 'error')
      this.SpinnerService.hide()
      this.SpinnerService.globalSpinnerSubject.next(false);
    }))
  }

  search() {
    if (this.searchText.trim() != "") {
      this.modes_to_show = this.modes.filter((company) => company.name.toLowerCase().includes(this.searchText.toLowerCase()));
    } else {
      this.modes_to_show = [...this.modes]
    }
  }
  showActionsModal(company: any) {
    this.showActions = true;
    this.selected_mode = company;
  }

  hideActionsModal() {
    this.showActions = false;
    this.selected_mode = null;
  }
  getModesByShoetypeRef$(ref:string) {
    this.SpinnerService.globalSpinnerSubject.next(true);
    this.SpinnerService.show();
    this.subscriptions.push(this.AdminService.getModesByShoetypeRef(ref).subscribe((response: any) => {
      this.modes = response.data;
      this.modes_to_show = [...this.modes]
      this.searchText = ""
      console.log(this.modes_to_show)
      this.SpinnerService.hide()
      this.SpinnerService.globalSpinnerSubject.next(false);
    }, (error) => {
      this.modes = []
      this.modes_to_show = [...this.modes]
      this.searchText = ""
      this.SpinnerService.hide()
      this.SpinnerService.globalSpinnerSubject.next(false);
    }))
  }
  toggleStatus(mode: any) {
    this.SpinnerService.show()
    this.subscriptions.push(this.StandardService.toggleActiveStatus(mode.ref, 'modes').subscribe({
      next: (response: any) => {
        this.messageService.set(response.message, 'green')
        this.SpinnerService.hide()
      }, error: (error) => {
        mode.is_active = !mode.is_active 
        this.SpinnerService.hide()
      }
    }))
  }
}