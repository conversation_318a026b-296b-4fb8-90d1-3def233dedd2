@import "../../../../../styles/variables";

.app-new-container {
    display: flex;

    img.more-infos {
        width: 1.7rem;
    }

    &>.section1 {
        height: calc(95vh - 120px) !important;
        overflow-y: auto;
        &>div {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        display: flex;
        flex-direction: column;
        align-items: flex-start;
        border-right: 0.1rem solid rgba($dark, 0.75);
        .informations {
            align-items: center;
            margin: .5rem 0;

            h2 {
                color: $primary;
                margin-right: 1rem;
            }

            h5 {
                font-weight: 600;
                font-size: 1.1rem;
                margin-left: 1.5rem;
            }
        }
    }

    &>.section2 {
        display: flex;
        flex-direction: column;

        .custom-fields{
            .informations {
                align-items: center;
                margin: 1.5rem 0;
                justify-content: space-between;
    
                h2 {
                    color: $primary;
                    margin-right: 1rem;
    
                    &>span {
                        display: inline-block;
                        width: 1.8rem;
                        height: 1.8rem;
                        border: 2px solid $primary;
                        font-size: 1.2rem;
                        font-weight: 700;
                        text-align: center;
                        line-height: 2rem;
                        border-radius: 1.8rem;
    
                    }
                }
            }
        }

        &>.model-3d {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-end;
        

        .lasts-container {
            border: 1px solid $secondary;
            // border-top: 0;
            border-radius: 1rem 0 1rem 1rem;

            .single-last {
                width: 100%;
                height: 100%;
                padding: 1rem;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .scene {
                    border-radius: 1rem;
                    border: 1px solid $secondary;
                    overflow: hidden;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }

        

        .informations {
            align-items: center;
            border: 1px solid $secondary;
            border-bottom: 0;
            border-radius: 1rem 1rem 0 0;
            padding-left: 1rem;
            
            position: relative;

            &::before{
                position: absolute;
                content: "";
                width: calc(100% + 30px );
                height: 2px;
                background-color: white;
                top: 100%;
                right: 0;
            }
            &::after{
                position: absolute;
                content: "";
                width: 2px;
                height: 30px;
                background-color: white;
                bottom: 0;
                right: 100%;
            }

            span.design {
                position: absolute;
                width: 32px;
                height: 32px;
                right: 100%;
                bottom: -1px;
                border-radius: 0 0 1rem 0;
                z-index: 1;
                border: 1px solid $secondary;
                border-top: 0;
                border-left: 0;
            }

            h2 {
                color: $primary;
                margin: 0;

                &>span {
                    display: inline-block;
                    width: 1.8rem;
                    height: 1.8rem;
                    border: 2px solid $primary;
                    font-size: 1.2rem;
                    font-weight: 700;
                    text-align: center;
                    line-height: 2rem;
                    border-radius: 1.8rem;

                }
            }
        }
        }

        .icon-div{
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            background-color: white;

        }
    }

    &>h1 {
        position: absolute;
        margin: 0;
        padding: 0;
        color: $primary;
        background-color: $back;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40%;
        text-align: center;
        border-bottom-left-radius: 3rem;
        border-bottom-right-radius: 3rem;
    }
    &>app-popup-ui{
        position: fixed;
        z-index: 2;
        .body-field-creation{
            width: 50vw;
            min-height: 50vh;
            display: flex;
            flex-direction: column;
        }
        form{
            border-right: 2px solid rgba($dark, 0.5);
            flex-grow: 1;
        }
        .delete-icon:hover{
            transform: scale(1.2);
            transition: all 200ms ease-in;
        }
    }

}