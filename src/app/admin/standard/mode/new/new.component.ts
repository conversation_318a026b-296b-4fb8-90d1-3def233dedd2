import { <PERSON>mpo<PERSON>, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from '@angular/core';
import { StaticDataService } from 'src/app/services/static.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import * as ENUMS from '../../../../shared/enums/generale'
import { Subscription } from 'rxjs';
import { LocalService } from 'src/app/services/local.service';
import { ActivatedRoute, Router } from '@angular/router';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { SceneService } from 'src/app/scene/main/scene.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { StandardService } from '../../standard.service';

@Component({
  selector: 'app-new-mode',
  templateUrl: './new.component.html',
  styleUrls: ['./new.component.scss']
})
export class NewModeComponent implements OnI<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {

  @ViewChild('modelast') modelast!: ElementRef;

  dateNow: Date = new Date();

  isLoading: boolean = false
  subscriptions: Subscription[] = []
  mode_last_scene: boolean = false
  mode_last: File | null = null;
  title: "Create" | "Update" = "Create"
  ENUMS: any = ENUMS;
  add_new_form: FormGroup;
  shoetype_ref: string = ""
  standard_ref: string = ""
  selected_shoetype : any = null;

  constructor(private StandardService: StandardService,
    private SpinnerService: SpinnerService,
    private SceneService: SceneService,
    private LocalService: LocalService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private MessagesService: MessagesService,
    public StaticDataService: StaticDataService
  ) { }

  ngOnInit(): void {
    this.SpinnerService.show();
    this.activatedRoute.paramMap.subscribe(params => {
      this.shoetype_ref = params.get('shoetype_ref')!;
      this.standard_ref = params.get('standard_ref')!;
      this.init_form();
      this.subscriptions.push(this.StandardService.getShoetypeInfosByRef(this.shoetype_ref).subscribe({
          next: (response: any) => {
            this.SpinnerService.hide();
            this.selected_shoetype = response.data
            this.add_new_form.controls['gender'].setValue(this.selected_shoetype.gender.slug);
            this.add_new_form.controls['category'].setValue(this.selected_shoetype.category.slug);
            this.add_new_form.controls['shoetype'].setValue(this.selected_shoetype.ref);
          },
          error: (err) => {
            this.SpinnerService.hide();
          }
        }))
    });

    
    this.subscriptions.push(this.SceneService.converted_file.subscribe({
      next: (file_converted) => {
        this.mode_last = file_converted.file
      }
    }))
  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }

  select_wide_width($event: any) {
    this.add_new_form.controls['wide_width_option'].setValue($event);
  }
  init_form() {
    this.add_new_form = new FormGroup({
      'reference': new FormControl('', [Validators.required]),
      'gender': new FormControl('', [Validators.required]),
      'category': new FormControl('', [Validators.required]),
      'shoetype': new FormControl('', [Validators.required]),
      'orientation': new FormControl('right', [Validators.required]),
      'unit': new FormControl('mm', [Validators.required]),
      'size': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'system_size': new FormControl('', [Validators.required]),
      'wide_width_option': new FormControl('', [Validators.required]),
      'front_insock_thickness': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'back_insock_thickness': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'height': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'height_type': new FormControl('', [Validators.required]),
      'ferrato': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'ferrato_type': new FormControl('', [Validators.required]),
      'last_bottom_lock_option': new FormControl('', [Validators.required]),
      'toe_shape_type': new FormControl('', [Validators.required]),
    });
  }

  onCreateMode() {
      if (this.add_new_form.valid) {
        this.SpinnerService.show();
        const formData = new FormData();
        this.mode_last != null ? formData.append('mode_last', this.mode_last, this.mode_last.name) : null;
        const data = this.add_new_form.value;
        Object.keys(data).forEach((key) => {
          if (data[key] != "" && data[key] != null) {
              formData.append(key, data[key]);
          }
        });
        this.subscriptions.push(this.StandardService.createNewMode(formData).subscribe({
          next: (response: any) => {
            this.SpinnerService.hide();
            this.MessagesService.set(response.message, "green");
            this.LocalService.selected_last = null
            this.router.navigateByUrl('/standard/'+ this.standard_ref + '/shoetype/' +  this.shoetype_ref + '/modes');
          },
          error: (err) => {
            this.SpinnerService.hide();
          }
        }))
      } else {
        console.log(this.add_new_form.errors)
        this.MessagesService.set('Fill All Required Fields')
      }
  }

  uploadModeLast() {
    if (!this.mode_last_scene) {
      this.modelast.nativeElement.click();
    }
  }
  lastChange(_$event: any, type: string) {
    let file = _$event.target.files[0]
    const extension = file.name.split(".").pop();
    if (['stl', 'obj'].includes(extension)) {
      this.mode_last_scene = true
      this.SceneService.show_stl.next({ name: 'mode', extension: extension, file: file })

    } else {
      this.MessagesService.set("Only Accept STL and OBJ Files", 'error')
    }
  }
}
