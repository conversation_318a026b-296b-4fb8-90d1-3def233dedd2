import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { AdminService } from '../services/admin.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { ActivatedRoute } from '@angular/router';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { StaticDataService } from 'src/app/services/static.service';
import { ValidatePassword } from 'src/app/shared/validators/password.validator';

@Component({
  selector: 'app-admin-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class CompanyUsersComponent implements OnInit, OnDestroy {
  subscriptions : Subscription[] = []
  users : any[] = []
  users_to_show : any[] = []
  company : any = null
  users_popup : boolean = false;
  actions_popup : boolean = false;
  connect_lastengineers_popup : boolean = false;
  change_password_popup : boolean = false;
  reset_password_popup : boolean = false;
  selected_user : any = null;
  isLoading : boolean = false;
  addUserForm: FormGroup;
  linkToLastengineersForm: FormGroup;
  changePasswordForm: FormGroup;
  searchText: string =  "";
  constructor(public AdminService:AdminService,
     private SpinnerService:SpinnerService,
     private StaticDataService:StaticDataService,
     private MessagesService:MessagesService,
     private route: ActivatedRoute){
  }
  ngOnInit(): void {
    this.SpinnerService.show()
     this.subscriptions.push(this.route.params.subscribe(params => {
      this.subscriptions.push(this.AdminService.getCompanyUsers(params['ref']).subscribe((data :any)=>{
          this.users = data.users;
          this.users_to_show = [...this.users]
          this.searchText = ""
          this.company = data.company;
          this.SpinnerService.hide()
          this.SpinnerService.globalSpinnerSubject.next(false);
        }, (error)=>{
          this.users = []
          this.users_to_show = []
          this.searchText = ""
          this.company = null;
          this.SpinnerService.hide()
          this.SpinnerService.globalSpinnerSubject.next(false);
      }))
   }));

    this.addUserForm = new FormGroup({
      ref: new FormControl({value: "", disabled: true}, Validators.required),
      first_name: new FormControl(null, Validators.required),
      last_name: new FormControl(null, Validators.required),
      email: new FormControl(null, Validators.required),
      role: new FormControl(null, Validators.required),
      system_size: new FormControl(null, Validators.required),
      country: new FormControl(null, Validators.required),
      city: new FormControl(null, Validators.required),
      address: new FormControl(null, Validators.required),
      password: new FormControl(null, Validators.required)
    });
    this.linkToLastengineersForm = new FormGroup({
      email: new FormControl(null, Validators.required),
      token: new FormControl(null, Validators.required),
      platform: new FormControl('test', Validators.required)
    });
    this.changePasswordForm = new FormGroup({
      password: new FormControl(null, [Validators.required, ValidatePassword]),
      confirm_password: new FormControl(null, Validators.required)
    }, { validators: [this.formGroupValidator] });
  }

  formGroupValidator: ValidatorFn = (
    formGroup: AbstractControl
  ): ValidationErrors | null => {
    return formGroup.value.password == formGroup.value.confirm_password ? null : { same_password_error: true };
  };

  
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }

  cancel(){
    this.users_popup = false;
    this.actions_popup = false;
    this.selected_user = null
    this.addUserForm.reset()
    this.connect_lastengineers_popup = false;
    this.change_password_popup = false;
    this.reset_password_popup = false;
  }
  showAddUser(){
    this.users_popup = true;
  }
  showActions(user:any){
    this.actions_popup = true;
    this.selected_user = user;
  }
  showInfos(user:any){
    this.users_popup = true;
    this.selected_user = user;
    this.addUserForm.patchValue({
      ref: user.ref,
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      role: user.user_role['slug'],
      country: user.country['slug'],
      city: user.city,
      system_size: user.system_size_slug['slug'],
    });
  }
  onSubmitUser(){
    if(this.selected_user) {
      this.onEditUser()
    } else {
      if(this.addUserForm.valid){
        this.isLoading = true;
        let data = {
          ...this.addUserForm.value,
          company: this.company.ref
        }
        this.subscriptions.push(this.AdminService.postNewUser(data).subscribe({
          next : (value) => {
              this.isLoading = false;
              this.cancel();
              this.ngOnInit()
            },
            error : (err) => {
              this.isLoading = false;
          },
        }))
      }
    }
  }

  onEditUser() {
    this.isLoading = true;
    let data = {
      ...this.addUserForm.value,
      company: this.company.ref
    }
    this.subscriptions.push(this.AdminService.updateUser(this.addUserForm.get('ref')?.value, data).subscribe({
      next : (value) => {
          this.isLoading = false;
          this.cancel();
          this.MessagesService.set("User Info updated Successfully")
          this.ngOnInit()
        },
        error : (err) => {
          // console.log(err)
          this.isLoading = false;
      },
    }))
  }

  select_contry($event: any) {
    this.addUserForm.controls['country'].setValue($event);
  }
  select_role($event: any) {
    this.addUserForm.controls['role'].setValue($event);
  }
  select_system_size($event: any) {
    this.addUserForm.controls['system_size'].setValue($event);
  }
  showLinkToLastEngineers(){
    this.connect_lastengineers_popup = true
  }
  linkToLastEngineers(){
    if(this.linkToLastengineersForm.valid){
      this.isLoading = true;
      let data = {
        ...this.linkToLastengineersForm.value,
        email_lastengineers: this.selected_user.email
      }
      this.subscriptions.push(this.AdminService.postLinkToLastengineers(data).subscribe({
        next : (response : any) => {
            this.isLoading = false;
            this.MessagesService.set(response.message, 'green');
            setTimeout(()=>{
              this.cancel();
              this.ngOnInit();
            }, 3000)
          },
          error : (err) => {
            this.isLoading = false;
        },
      }))
    }
  }
  changePasswordPopup(type_pop: 'change_password' | 'reset_password'){
    this.change_password_popup = type_pop == 'change_password'
    this.reset_password_popup = type_pop == 'reset_password'
  }
  changePassword(){
    if(this.changePasswordForm.valid){
      this.isLoading = true;
      let data = {
        password: this.changePasswordForm.get('password')?.value,
        email: this.selected_user.email
      }
      this.subscriptions.push(this.AdminService.postChangePassword(data).subscribe({
        next : (response : any) => {
            this.isLoading = false;
            this.MessagesService.set(response.message, 'green');
            setTimeout(()=>{
              this.cancel();
              this.ngOnInit();
            }, 3000)
          },
          error : (err) => {
            this.isLoading = false;
            this.MessagesService.set(err, 'error');
        },
      }))
    }
  }
  resetPassword(){
      this.isLoading = true;
      this.subscriptions.push(this.AdminService.postResetPassword({email : this.selected_user.email}).subscribe({
        next : (response : any) => {
            this.isLoading = false;
            this.MessagesService.set(response.message, 'green');
            this.cancel();
          },
          error : (err) => {
            this.isLoading = false;
            this.MessagesService.set(err, 'error');
        },
      }))
  }

  loginAsAdmin(user_id:number){
    this.isLoading = true;
    this.subscriptions.push(this.AdminService.loginAsAdmin(user_id).subscribe({
      next : (response:any) => {
          this.isLoading = false;
          localStorage.setItem('token', response.token)
          window.location.reload();
        },
        error : (err) => {
          this.isLoading = false;
          this.MessagesService.set('Error on log in as admin', 'error')
      },
    }))
  }

  get countries() {
    return []
  }
  get roles() {
    return this.StaticDataService.roles
  }
  get system_sizes() {
    return this.StaticDataService.system_sizes
  }

  search(){
    if (this.searchText.trim() != ""){
      this.users_to_show = this.users.filter((user) => user.email.toLowerCase().includes(this.searchText.toLowerCase()));
    }else{
      this.users_to_show = [...this.users]
    }
  }

}