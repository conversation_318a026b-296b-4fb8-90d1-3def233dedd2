<div class="full-body">
    <div class="w-100 h-100 d-flex flex-column p-2 admin-container overflow-y-auto">
        <div class="header">
            <button class="btn-back" [routerLink]="['/admin/companies']"> <img src="assets/icons/arrow-to-left.svg"
                    alt="back button">{{ 'COMMON.BACK_BTN' | translate }}</button>
            <div class="d-flex align-items-center title">
                <img src="assets/icons/user-icon.svg" alt="Company Icon">
                <p class="m-0 pt-1">{{ 'USERS.NAV_ITEM' | translate }}</p>
            </div>
            <div class="search">
                <input type="search" [placeholder]="'COMMON.SEARCH_BTN' | translate" (search)="search()" (keyup)="search()" [(ngModel)]="searchText">
            </div>
        </div>
        <div class="flex-grow-1 d-flex flex-column mx-auto w-100 justify-content-start">
            <div class="flex-grow-1 table">
                <table class="table listing-table">
                    <thead class="table-dark sticky">
                        <tr>
                            <th scope="col">Company</th>
                            <th scope="col">{{ 'USERS.FIRST_NAME' | translate }}</th>
                            <th scope="col">{{ 'USERS.LAST_NAME' | translate }}</th>
                            <th scope="col">{{ 'IDENTIFICATION.ROLE' | translate }}</th>
                            <th scope="col">{{ 'USERS.EMAIL' | translate }}</th>
                            <th scope="col">{{ 'USERS.LAST_CONNEXION' | translate }}</th>
                            <th scope="col">{{ 'STATUS.ENABLED' | translate }}</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>

                    <tbody *ngIf="users.length > 0">
                        <tr *ngFor="let user of users_to_show">
                            <td>
                                <span>{{ company.name | empty}}</span>
                            </td>
                            <td>
                                <span>{{ user.first_name | empty}}</span>
                            </td>
                            <td>
                                <span>{{ user.last_name | empty}}</span>
                            </td>
                            <td>
                                <span>{{ user.user_role['name'] | empty}}</span>
                            </td>
                            <td>
                                <span>{{ user.email | empty}}</span>
                            </td>
                            <td>
                                <span>{{ user.last_connexion | empty}}</span>
                            </td>
                            <!-- <td>
                                <span>{{ user.users_counter | empty}} Users</span>
                            </td>
                            <td style="font-size: 1rem;">
                                IF : {{ user.max_if | empty }} <br> 
                                AF : {{ user.max_af | empty }}
                            </td> -->
                            <td>
                                <img src="assets/icons/{{ user.enabled ? 'enabled' : 'disabled' }}-icon.svg"
                                    alt="enabled icon" width="30px">
                            </td>
                            <td>
                                <button (click)="showInfos(user)" class="mini-btn-gray btn-primary py-0 m-1">
                                    {{ 'COMMON.INFOS' | translate}}
                                </button>
                                <button class="mini-btn-gray btn-primary py-0 m-1">
                                    {{ 'HOME.ACTIVITY' | translate }}
                                </button>
                                <br>
                                <button (click)="showActions(user)" class="mini-btn-primary btn-primary py-0 m-1">
                                    {{ 'TABLE_HEADERS.ACTION' | translate }}
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <h1 class="w-100 text-center pt-3" *ngIf="!(users.length > 0)">{{ 'USERS.EMPTY_LIST_MESSAGE' | translate }}</h1>
            </div>
            <div class="w-95 mx-auto p-2">
                <button class="btn btn-primary px-3 rounded-40" (click)="showAddUser()">
                    <img class="p-1 pe-2" src="assets/icons/plus-icon.svg" alt="plus icon">
                    {{ 'USERS.ADD_USER' | translate }}
                </button>
            </div>

        </div>
    </div>
</div>


<app-popup-ui (cancel)="cancel()" *ngIf="actions_popup" class="w-60">
    <h4 title class="text-center text-primary">
        {{ 'USERS.ACTIONS' | translate }}
    </h4>
    <div message style="width: 50vw;">

        <div class=" w-100 d-flex flex-column btn-actions">
            <div>
                <h5 class="sub-title" >{{ 'USERS.PASSWORD' | translate }}</h5>
                <div class="d-flex">
                    <button class="btn btn-primary btn-no-border" (click)="changePasswordPopup('change_password')">{{ 'USERS.CHANGE_PASSWORD' | translate }}</button>
                    <button class="btn btn-primary btn-no-border" (click)="changePasswordPopup('reset_password')">{{ 'Reset Password' | translate }}</button>
                </div>
            </div>
            <div>
                <h5 class="sub-title" >{{ 'USERS.ACTIVATION' | translate }}</h5>
                <div class="d-flex">
                    <button class="btn btn-primary btn-no-border">{{ 'USERS.RESET_ACTIVATION' | translate }}</button>
                    <button class="btn btn-primary btn-no-border">{{ 'USERS.MANUAL_ACTIVATION' | translate }}</button>
                    <button class="btn btn-primary btn-no-border" (click)="showLinkToLastEngineers()" >{{ 'USERS.CONNECT_TO_LAST' | translate }}</button>                    
                </div>
            </div>
            <div>
                <h5 class="sub-title" >{{ 'USERS.LOG_IN' | translate }}</h5>
                <div class="d-flex">
                    <button class="btn btn-primary btn-no-border" (click)="loginAsAdmin(selected_user.id)">{{ 'USERS.LOG_IN_AS_ADMIN' | translate }}</button>
                </div>
            </div>
            <div>
                <h5 class="sub-title" >{{ 'USERS.DELETE' | translate }}</h5>
                <div class="d-flex">
                    <button class="btn btn-primary btn-no-border">{{ 'USERS.DELETE' | translate }}</button>
                </div>
            </div>
        </div>

    </div>
    <app-spinner-html spinner *ngIf="isLoading"></app-spinner-html>
</app-popup-ui>



<app-popup-ui (cancel)="cancel()" *ngIf="users_popup" class="w-60">
    <h4 title class="text-center text-primary">
       {{ (selected_user ? "USERS.INFO" :  "USERS.NEW") | translate }}
    </h4>
    <form message class="row pe-4" (ngSubmit)="onSubmitUser()" [formGroup]="addUserForm" style="width: 50vw; position: relative;">

        <div class="col w-50">
            <app-field name="ID" *ngIf="selected_user">
                <input autocomplete="off" formControlName="ref" class="dm-text-input" type="text"
                    name="ref" id="ref">
            </app-field>
            <app-field name="USERS.FIRST_NAME" [required]="!selected_user">
                <input autocomplete="off" formControlName="first_name" class="dm-text-input" type="text"
                    name="first_name" id="first_name">
            </app-field>
            <app-field name="USERS.LAST_NAME" [required]="!selected_user">
                <input autocomplete="off" formControlName="last_name" class="dm-text-input" type="text"
                    name="last_name" id="last_name">
            </app-field>
            <app-searchable-select name="IDENTIFICATION.ROLE" [list]="roles" [required]="!selected_user"
            [selected_item]="selected_user ? selected_user.user_role : ''"
            (select_event)="select_role($event)"></app-searchable-select>
            <app-field name="IDENTIFICATION.CITY" [required]="!selected_user">
                <input autocomplete="off" formControlName="city" class="dm-text-input" type="text"
                    name="city" id="city">
            </app-field>
            <app-field name="IDENTIFICATION.ADDRESS" [required]="!selected_user">
                <input autocomplete="off" formControlName="address" class="dm-text-input" type="text"
                    name="address" id="address">
            </app-field>
        </div>
        
        <div class="col w-50 ms-4">
            <app-searchable-select name="BACKEND.Country" [list]="countries" [required]="!selected_user"
            [placeholder]="selected_user ? selected_user.country: ('IDENTIFICATION.COUNTRY_PLACEHOLDER' | translate)"
            [selected_item]="selected_user ? selected_user.country : ''"
            (select_event)="select_contry($event)"></app-searchable-select>
            <app-field name="Email" [required]="!selected_user">
                <input autocomplete="off" formControlName="email" class="dm-text-input" type="email"
                    name="email" id="email">
            </app-field>
            <app-searchable-select name="System Size" [list]="system_sizes"  [required]="!selected_user" 
            [selected_item]="selected_user ? selected_user.system_size_slug : ''"
            (select_event)="select_system_size($event)"></app-searchable-select>
            <app-field name="USERS.PASSWORD" *ngIf="!selected_user">
                <input autocomplete="off" formControlName="password" class="dm-text-input" type="text"
                    [placeholder]="'USERS.PASSWORD' | translate" name="password" id="password">
            </app-field>
        </div>
       
        <div class="d-flex justify-content-center mt-5">
            <button class="btn btn-primary px-4" [disabled]="addUserForm.invalid && !selected_user" type="button" (click)="onSubmitUser()">
              <img src="assets/icons/check-icon.svg" alt="close icon" width="16px" class="me-2">
              {{ (selected_user ? "COMMON.SAVE_BTN" :  "USERS.ADD_USER") | translate }}
            </button>
          </div>
        

    </form>
    <app-spinner-html spinner *ngIf="isLoading"></app-spinner-html>
</app-popup-ui>




<app-popup-ui (cancel)="cancel()" *ngIf="connect_lastengineers_popup" class="w-60">
    <h4 title class="text-center text-primary">
        Connect to Last Engineers
    </h4>
    <form message class="d-flex p-3 py-5" style="width: 30vw;" (ngSubmit)="linkToLastEngineers()" autocomplete="off" [formGroup]="linkToLastengineersForm">
        <div class="w-100 p-2">
            <app-field name="Email" [required]="true">
                <input autocomplete="off" formControlName="email" class="dm-text-input" type="text"
                    placeholder="Email" name="email" id="email">
            </app-field>
            <app-field name="Code Activation" [required]="true">
                <input autocomplete="off" formControlName="token" class="dm-text-input" type="text"
                    placeholder="Code Activation" name="token" id="token">
            </app-field>
            <app-field name="platform" [required]="true">
                <div class="dm-radio-input-v1 w-100 ps-4">
                    <ng-container *ngFor="let platform of [ { name : 'Local', slug : 'local' }, { name : 'Test', slug : 'test' }, { name : 'Prod', slug : 'prod' } ]">
                        <input autocomplete="off" formControlName="platform" type="radio" [value]="platform.slug"
                            name="platform" id="{{ 'platform_' + platform.slug }}" >
                        <label for="{{ 'platform_' + platform.slug}}">{{ platform.name }}</label>
                    </ng-container>
                </div>
            </app-field>         
        </div>
    </form>
    <ng-container actions>
        <div class="w-100 d-flex justify-content-center">
            <button [disabled]="linkToLastengineersForm.invalid" class="btn btn-primary" (click)="linkToLastEngineers()">Connect Account</button>
        </div>
    </ng-container>
    <app-spinner-html spinner *ngIf="isLoading"></app-spinner-html>
</app-popup-ui>

<app-popup-ui (cancel)="cancel()" *ngIf="change_password_popup" class="w-60">
    <h4 title class="text-center text-primary">
        Change Password
    </h4>
    <form message class="d-flex p-3" style="width: 30vw;" (ngSubmit)="changePassword()" autocomplete="off" [formGroup]="changePasswordForm">
        <div class="w-100 p-2">
            <app-field name="Password" [required]="true">
                <input autocomplete="off" formControlName="password" class="dm-text-input" type="text"
                    placeholder="Password" name="password" id="password">
            </app-field>
            <div class="text-danger font-1" *ngIf="changePasswordForm.get('password')?.errors?.['complex_password']">
                Password must be at least 10 characters long, contain uppercase, lowercase, number, and special character.
              </div>
            <app-field name="Confirm Password" [required]="true">
                <input autocomplete="off" formControlName="confirm_password" class="dm-text-input" type="text"
                    placeholder="Confirm Password" name="confirm_password" id="confirm_password">
            </app-field>
            <div class="text-danger font-1" *ngIf="changePasswordForm.hasError('same_password_error')">
                Passwords do not match.
              </div>       
        </div>
    </form>
    <ng-container actions>
        <div class="w-100 d-flex justify-content-center">
            <button [disabled]="changePasswordForm.invalid" class="btn btn-primary" (click)="changePassword()">Change</button>
        </div>
    </ng-container>
    <app-spinner-html spinner *ngIf="isLoading"></app-spinner-html>
</app-popup-ui>
<app-popup-ui (cancel)="cancel()" *ngIf="reset_password_popup" class="w-60">
    <h4 title class="text-center text-primary">
        Reset Password
    </h4>
    <div message>
        <div class="p-3">
            <h5>
                Clicking this button will send an automated email to the user containing a secure link to change their password. The link will expire after 24 hours for security purposes.
            </h5>
        </div>
    </div>
    <ng-container actions>
        <div class="w-100 d-flex justify-content-center">
            <button class="btn btn-danger" (click)="cancel()">Cancel</button>
            <button class="btn btn-primary" (click)="resetPassword()">Reset</button>
        </div>
    </ng-container>
    <app-spinner-html spinner *ngIf="isLoading"></app-spinner-html>
</app-popup-ui>