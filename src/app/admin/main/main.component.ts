import { Component, inject, OnDestroy, signal } from '@angular/core';
import { Subscription } from 'rxjs';
import { TicketService } from 'src/app/services/ticket.service';
import { CustomTranslateService } from 'src/app/services/custom-translate-service.service';
import { UserService } from 'src/app/services/user.service';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';

@Component({
  selector: 'app-admin-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss']
})

export class AdminMainComponent implements OnDestroy {
  unreadTickets = signal<number>(0);
  subscriptions: Subscription[] = [];
  ticketService = inject(TicketService);

  constructor(public UserService: UserService, private spinnerService: SpinnerService, private customTranslateService: CustomTranslateService) {
    this.spinnerService.globalSpinnerSubject.next(true);

    this.customTranslateService.languageLoaded$.subscribe(() => this.spinnerService.globalSpinnerSubject.next(false));

    this.subscriptions.push(this.ticketService.getUnreadTicketsData().subscribe((unreadTicketNumber: any) => {
      this.unreadTickets.set(unreadTicketNumber['ticketCount']);
    }));
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(s => s.unsubscribe());
  }
}
