<div class="full-body">
    <div class="w-100 h-100 d-flex flex-column p-2 admin-container overflow-y-auto">
        <h1 class="title"><h1 [innerHTML]="'Admin Dashboard' | translate"></h1>
        <button class="btn-back" [routerLink]="['/home']"> <img src="assets/icons/arrow-to-left.svg" alt="back button">
            {{ 'Back' | translate }}
        </button>
        </h1>
        <div class="d-flex flex-column mx-auto w-90 justify-content-start">
            <h3 class="w-100 text-left border-bottom">{{ 'Management' | translate }}</h3>

            <div class="w-75 d-flex align-items-center justify-content-around pt-5 mx-auto flex-grow-1">
                <button [routerLink]="['/admin/glossary']" class="btn-home pointer px-4">
                    <!-- <img src="assets/icons/i18n-icon.svg" width="25" alt="Scanner Icon"> -->
                    <svg fill="#000000" width="25px" height="25px" viewBox="0 0 52 52" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><path d="M39,18.67H35.42l-4.2,11.12A29,29,0,0,1,20.6,24.91a28.76,28.76,0,0,0,7.11-14.49h5.21a2,2,0,0,0,0-4H19.67V2a2,2,0,1,0-4,0V6.42H2.41a2,2,0,0,0,0,4H7.63a28.73,28.73,0,0,0,7.1,14.49A29.51,29.51,0,0,1,3.27,30a2,2,0,0,0,.43,4,1.61,1.61,0,0,0,.44-.05,32.56,32.56,0,0,0,13.53-6.25,32,32,0,0,0,12.13,5.9L22.83,52H28l2.7-7.76H43.64L46.37,52h5.22Zm-15.3-8.25a23.76,23.76,0,0,1-6,11.86,23.71,23.71,0,0,1-6-11.86Zm8.68,29.15,4.83-13.83L42,39.57Z"/></svg>
                    Glossary
                </button>
                <button [routerLink]="['/admin/companies']" class="btn-home pointer px-4">
                    <!-- <img src="assets/icons/company-icon.svg" alt="Company Icon"> -->
                    <svg width="25" height="32" viewBox="0 0 31 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M28.7857 21.1667H31V28.4C31 30.1091 29.5098 31.5 27.6786 31.5H3.32143C1.49021 31.5 0 30.1091 0 28.4V21.1667H2.21429V28.4C2.21429 28.9704 2.71139 29.4333 3.32143 29.4333H27.6786C28.2886 29.4333 28.7857 28.9704 28.7857 28.4V21.1667ZM28.7857 16C28.7857 16.5704 28.2886 17.0333 27.6786 17.0333H19.9286V13.9333H11.0714V17.0333H3.32143C2.71139 17.0333 2.21429 16.5704 2.21429 16V8.63853C4.47396 8.36883 10.3606 7.73333 15.5 7.73333C20.6394 7.73333 26.526 8.36883 28.7857 8.63853V16ZM17.7143 19.6156C17.3224 19.8347 16.5883 20.1333 15.5 20.1333C14.4084 20.1333 13.6732 19.8326 13.2857 19.6177V16H17.7143V19.6156ZM12.1786 3.2435C12.9204 2.9707 14.229 2.56667 15.5 2.56667C16.771 2.56667 18.0796 2.9707 18.8214 3.2435V5.74107C17.715 5.69371 16.6076 5.6689 15.5 5.66667C14.4061 5.66667 13.2868 5.6956 12.1786 5.74107V3.2435ZM30.0501 6.71033C29.8176 6.67933 25.7721 6.14717 21.0357 5.85577V1.92807L20.4235 1.64183C20.3238 1.59637 17.9468 0.5 15.5 0.5C13.0532 0.5 10.6762 1.59637 10.5765 1.64183L9.96428 1.92807V5.85577C5.22793 6.14717 1.18243 6.67933 0.949929 6.71033L0 6.83743V16C0 17.7091 1.49021 19.1 3.32143 19.1H11.0714V20.5611L11.3958 20.8639C11.542 21.0013 12.9015 22.2 15.5 22.2C18.0985 22.2 19.458 21.0013 19.6042 20.8639L19.9286 20.5611V19.1H27.6786C29.5098 19.1 31 17.7091 31 16V6.83743L30.0501 6.71033Z" fill="#212529"/>
                        </svg>
                    {{ 'Companies' | translate }}
                </button>
                <button [routerLink]="['/standards']" class="btn-home pointer px-4">
                    <!-- <img src="assets/icons/hand-scanner-icon.svg" alt="Scanner Icon"> -->
                    <svg width="25" height="25" viewBox="0 0 17 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 5.84375C17 2.60313 14.3969 0 11.1562 0H2.125C2.07188 0 2.01875 0 1.96562 0C1.9125 0 1.85937 0 1.80625 0C0.85 0 0 0.85 0 1.85938V6.10937C0 7.11875 0.85 7.96875 1.85938 7.96875C1.9125 7.96875 1.96563 7.96875 2.01875 7.96875C2.07188 7.96875 2.125 7.96875 2.17813 7.96875H7.49063V8.925C7.49063 9.5625 7.225 10.1469 6.8 10.5719C6.53437 10.8375 6.42813 11.1562 6.42813 11.475C6.42813 12.1656 7.0125 12.75 7.70313 12.75H9.775L10.8375 15.8844C10.8906 16.0438 10.8375 16.15 10.7844 16.2562C10.625 16.4156 10.5188 16.4688 10.3594 16.4688C9.61563 16.4688 9.03125 17.0531 9.03125 17.7969C9.03125 18.5406 9.61563 19.125 10.3594 19.125H15.0875C16.15 19.125 17 18.275 17 17.2125C17 17 16.9469 16.7875 16.8937 16.6281L14.025 7.96875H14.875C16.0438 7.96875 17 7.0125 17 5.84375ZM11.1562 1.0625C13.2281 1.0625 14.9813 2.39062 15.6719 4.25H7.4375C5.57813 4.25 4.0375 2.86875 3.77188 1.0625H11.1562ZM2.65625 6.10937C2.65625 6.48125 2.39062 6.8 1.96562 6.90625C1.43437 6.85312 1.00937 6.375 1.00937 5.84375V2.125C1.00937 1.59375 1.43437 1.11562 1.96562 1.0625C2.39062 1.11562 2.65625 1.4875 2.65625 1.85938V6.10937ZM7.65 11.6875C7.54375 11.6875 7.4375 11.5812 7.4375 11.475C7.4375 11.4219 7.4375 11.3687 7.49063 11.3156C8.075 10.7312 8.44688 9.93437 8.5 9.08437L9.35 11.6875H7.65ZM15.8844 16.9469C15.8844 17.0531 15.9375 17.1062 15.9375 17.2125C15.9375 17.6906 15.5656 18.0625 15.0875 18.0625H10.3594C10.2 18.0625 10.0938 17.9562 10.0938 17.7969C10.0938 17.6375 10.2 17.5312 10.3594 17.5312C10.8375 17.5312 11.2625 17.3187 11.5813 16.8937C11.8469 16.5219 11.9531 15.9906 11.7938 15.5656L10.625 12.1125C10.625 12.0594 10.625 12.0594 10.5719 12.0063L9.24375 7.96875H12.9094L15.8844 16.9469ZM3.55938 6.90625C3.66563 6.64062 3.71875 6.375 3.71875 6.10937V3.50625C4.62188 4.56875 5.95 5.3125 7.4375 5.3125H15.8844C15.8844 5.47187 15.9375 5.68437 15.9375 5.84375C15.9375 6.42812 15.4594 6.90625 14.875 6.90625H3.55938Z" fill="#212529"/>
                        </svg>
                    {{ 'Standards' | translate }}
                </button>
                <button [routerLink]="['/admin/support']" class="btn-home pointer px-4 position-relative">
                    <!-- <img src="assets/icons/message-icon.svg" alt="Support Icon"> -->
                    <svg width="25" height="25" viewBox="0 0 17 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.12516 0.125H14.8752C15.6576 0.125 16.2918 0.759263 16.2918 1.54167V11.4583C16.2918 12.2407 15.6576 12.875 14.8752 12.875H2.12516C1.34276 12.875 0.708496 12.2407 0.708496 11.4583V1.54167C0.708496 0.759263 1.34276 0.125 2.12516 0.125ZM2.12516 4.81283V11.4583H14.8752V4.81313L8.50015 8.00064L2.12516 4.81283ZM2.12516 3.22891L8.50018 6.41674L14.8752 3.22925V1.54167H2.12516V3.22891Z" fill="#212529"/>
                        </svg>
                    {{ 'Support' | translate }}
                    <app-notification [unreadTickets]="unreadTickets()"></app-notification>
                </button>
            </div>

            <section class="d-flex flex-column align-items-center w-100 mt-3 activities">
                <h3 class="w-100 text-left border-bottom">
                    {{ 'Last 24 hours' | translate }}
                </h3>
                <div class="w-100 d-flex flex-wrap justify-content-around">
                    <p class="w-30">{{ 'Last' | translate | dash }} {{ 'Inventory' | translate }} : 0</p>
                    <p class="w-30">{{ 'Last' | translate | dash }} {{ 'Analyse' | translate }} : 0</p>
                    <p class="w-30">{{ 'Last Connexion' | translate }} : {{ '15:00:48 22/09/2025' }}</p>
                    <p class="w-30">{{ 'Last' | translate | dash }} {{ 'Optimisation' | translate }} : 0</p>
                    <p class="w-30">{{ 'Last' | translate | dash }} {{ 'Modification' | translate }} : 0</p>
                    <p class="w-30">{{ 'Last' | translate | dash }} {{ 'Comparison' | translate }} : 0</p>
                </div>
            </section>

            <!-- <section class="d-flex flex-column align-items-center w-100 mt-3 activities">
            <h3 class="w-100 text-left border-bottom">
                {{ 'Last 7 days' | translate }}
            </h3>
            <div class="w-100 d-flex flex-wrap justify-content-around">
                <p class="w-30">{{ 'HOME.DATABASE_STAT' | translate }} : 0</p>
                <p class="w-30">{{ 'FITTING.LIST_NAME' | translate | dash }} {{ 'FITTING_EVALUATION.NAV_ITEM' | translate }} : 0</p>
                <p class="w-30">{{ 'USERS.LAST_CONNEXION' | translate }} : {{ UserService.user.last_connexion }}</p>
                <p class="w-30">{{ 'HOME.FOOT_AVG_STAT' | translate }}: 0</p>
                <p class="w-30">{{ 'FITTING.LIST_NAME' | translate | dash }} {{ 'FITTING.OPTIMISATION_NAV_ITEM' | translate }} : 0</p>
                <p class="w-30">{{ 'FITTING.LIST_NAME' | translate | dash }} {{ 'FITTING_MODIFICATION.NAV_ITEM' | translate }} : 0</p>
            </div>
        </section> -->

        </div>
    </div>
</div>