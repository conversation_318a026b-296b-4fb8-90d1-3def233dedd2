import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdminComponent } from './admin.component';
import { SharedModule } from '../shared/shared.module';
import { LayoutsModule } from '../layouts/layouts.module';
import { SceneModule } from '../scene/scene.module';
import { LastInventoryModule } from '../inventory/last-inventory.module';
import { TemplateRoutingModule } from './admin-routing.module';
import { AdminMainComponent } from './main/main.component';
import { CompanyUsersComponent } from './users/users.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GlossaryComponent } from './glossary/glossary.component';
import { RecaptchaModule } from 'ng-recaptcha';
import { AdminCompanyComponent } from './company/company.component';
// import { StandardComponent } from './standard/standard.component';
import { NewModeComponent } from './standard/mode/new/new.component';
import { ShoetypeComponent } from './standard/shoetype/shoetype.component';
import { ModeComponent } from './standard/mode/mode.component';

@NgModule({
  declarations: [
    AdminComponent,
    AdminMainComponent,
    AdminCompanyComponent,
    CompanyUsersComponent,
    // StandardComponent,
    ShoetypeComponent,
    ModeComponent,
    NewModeComponent,
    GlossaryComponent
  ],
  imports: [
    SharedModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    LayoutsModule,
    SceneModule,
    // LastInventoryModule,
    TemplateRoutingModule,
    RecaptchaModule
  ],
  exports: [
    AdminComponent
  ]
})
export class AdminModule { }
