// admin/company/all

import { Injectable } from '@angular/core';
import { HttpClient } from "@angular/common/http";
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private companies: any[] = [];
  constructor(private http: HttpClient, private TranslateService: TranslateService) { }

  getAllCompanies(){
    return this.http.get(environment.api_url + 'api/'+ this.lang +'/admin/companies');
  }
  getCompanyUsers(id:string){
    return this.http.get(environment.api_url + 'api/'+ this.lang +'/admin/companies/' + id +'/users');
  }
  postNewCompany(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/company/new', data);
  }
  updateCompany(ref: any, data:any){
    return this.http.put(environment.api_url + 'api/'+ this.lang +'/admin/company/' + ref, data);
  }
  assignLanguagesToCompany(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/company/languages', data);
  }
  postNewUser(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/user/new', data);
  }
  updateUser(ref: any, data:any){
    return this.http.put(environment.api_url + 'api/'+ this.lang +'/admin/user/' + ref, data);
  }
  postLinkToLastengineers(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/lastengineers/connect', data);
  }
  postChangePassword(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/change_password', data);
  }
  postResetPassword(data:any){
    return this.http.post(environment.api_url + 'password/reset-request', data);
  }

  loginAsAdmin(user_id:number){
    return this.http.get(environment.api_url + 'api/'+ this.lang +'/admin/impersonate/' + user_id);
  }

  setCompanies(companies: any[]) {
    this.companies = companies;
  }

  getCompanies() {
    return this.companies;
  }

  postNewStandard(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/standards/create', data);
  }
  postUpdateStandard(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/company/new', data);
  }
  getAllStandards(){
    return this.http.get(environment.api_url + 'api/'+ this.lang +'/admin/standards/list');
  }
  deleteStandardById(standard_id:number){
    return this.http.delete(environment.api_url + 'api/'+ this.lang +'/admin/standards/' + standard_id + '/delete');
  }

  postNewShoetype(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/shoetypes/create', data);
  }
  postUpdateShoetype(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/company/new', data);
  }
  getShoetypesByStandardRef(ref:string){
    return this.http.get(environment.api_url + 'api/'+ this.lang +'/admin/shoetypes/by-standard/' + ref);
  }
  
  deleteShoetypeById(shoetype_id:number){
    return this.http.delete(environment.api_url + 'api/'+ this.lang +'/admin/shoetypes/' + shoetype_id + '/delete');
  }

  postNewMode(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/modes/create', data);
  }
  postUpdateMode(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/admin/company/new', data);
  }
  getModesByShoetypeRef(ref:string){
    return this.http.get(environment.api_url + 'api/'+ this.lang +'/admin/modes/by-shoetype/' + ref);
  }
  deleteModeById(shoetype_id:number){
    return this.http.delete(environment.api_url + 'api/'+ this.lang +'/admin/modes/' + shoetype_id + '/delete');
  }

  get lang(){
    // return this.TranslateService.getDefaultLang() ?? localStorage.getItem('lang');
    return 'en';
  }
}
