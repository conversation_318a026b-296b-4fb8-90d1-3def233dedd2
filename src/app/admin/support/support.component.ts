import { Component, inject, OnDestroy, OnInit, signal } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { catchError, of, Subscription } from 'rxjs';
import { SortDirectionEnum } from 'src/app/enums/columns-sorting';
import { Message, Ticket, ticketInitialization } from 'src/app/models/ticket';
import { SearchService } from 'src/app/services/search.service';
import { SortService } from 'src/app/services/sort.service';
import { TicketService } from 'src/app/services/ticket.service';
import { UserService } from 'src/app/services/user.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-admin-support',
    templateUrl: './support.component.html',
    styleUrls: ['./support.component.scss']
})

export class SupportComponent implements OnInit, OnDestroy {
    TABLE_HEADERS = [
        { header: 'Ticket Number', sortKey: 'ref' },
        { header: 'Subject', sortKey: 'subject' },
        { header: 'User', sortKey: 'user' },
        { header: 'Date of Creation', sortKey: 'createdAt' },
        { header: 'Date of Closing', sortKey: 'closedAt' },
        { header: 'Solved ?', sortKey: 'closedAt' }
    ];
    pagination = {
        all: 0,
        count: 0,
        current: 0,
        pageSize: 0
    };
    pagination_showing_list: any[] = [];
    subscriptions: Subscription[] = [];
    apiUrl = environment.api_url;
    // Signals
    isError = signal<boolean>(false);
    isAttachError = signal<boolean>(false);
    tickets = signal<Ticket[]>([]);
    selectedTicket = signal<Ticket>(ticketInitialization);
    selectedPage = signal<number>(0);
    showTicket = signal(false);
    showAttachments = signal(false);
    isAddingTicket = signal(false);
    isAdmin = signal<boolean>(false);
    isAdminPath = signal<boolean>(false);
    isAttachLoading = signal<boolean>(false);
    isTicketsLoading = signal<boolean>(false);
    imagePreviews = signal<{ url: string | ArrayBuffer | null }[]>([]);
    sortBy = signal<string>('createdAt');
    hoveredSortIcon = signal<string>('');
    ticketsToShow = signal<Ticket[]>([]);
    tableHeaders = signal<any[]>([]);
    unreadTicketIds = signal<number[]>([]);
    adminAsUserUnreadTicketIds = signal<number[]>([]);
    attachments = signal<any[]>([]);
    //Variables
    siteKey: string = environment.recaptcha.siteKey;
    // Forms
    ticketToSearch: FormControl;
    recaptcha: FormControl;
    ticket: FormGroup;
    // Services
    private searchService = inject(SearchService);
    private spinnerService = inject(SpinnerService);
    private ticketService = inject(TicketService);
    private messagesService = inject(MessagesService);
    private sortService = inject(SortService);
    private userService = inject(UserService);
    private router = inject(Router);

    ngOnInit(): void {
        const currentUrl = this.router.url;
        this.sortService.setSortConfig(this.sortBy(), SortDirectionEnum.DESCENDANT);
        this.isAdmin.set(this.userService.isUserAdmin);
        this.isAdminPath.set(currentUrl.split('/').includes('admin'));
        this.tableHeaders.set(this.TABLE_HEADERS.map(header => {
            return { ...header, sortDirection: SortDirectionEnum.DESCENDANT }
        }));
        if (!this.userService.isUserAdmin) {
            this.tableHeaders.mutate(prev => prev.splice(2, 1));
        }
        this.ticketToSearch = new FormControl('');
        this.fetchTickets$();
        this.ticket = new FormGroup({
            content: new FormControl('', Validators.required),
            subject: new FormControl({ value: '', disabled: true }, Validators.required),
        });
        this.recaptcha = new FormControl(null, Validators.required);
        this.ticketToSearch.valueChanges.subscribe(t => {
            if (t != "") {
                this.ticketsToShow.update(
                    () => this.tickets().filter(
                        s => s.ref.toLowerCase().includes(this.ticketToSearch.value.toLowerCase()) ||
                            s.subject.toLowerCase().includes(this.ticketToSearch.value.toLowerCase()) ||
                            (this.isAdmin() && s.user_name.toLowerCase().includes(this.ticketToSearch.value.toLowerCase())) ||
                            s.created_at.toLowerCase().includes(this.ticketToSearch.value.toLowerCase()) ||
                            s.closed_at?.toLowerCase().includes(this.ticketToSearch.value.toLowerCase())
                    )
                );
            } else {
                this.ticketsToShow.set([...this.tickets()]);
            }
        });

        this.subscriptions.push(this.searchService.search.subscribe(({ page }) => {
            if (!!this.page_count) {
                this.fetchTickets$(page);
                this.selectedPage.set(page);
            }
        }));

        this.subscriptions.push(this.ticketService.getAdminAsUserUnreadTicketsData().subscribe((unreadTicketNumber: any) => {
            this.adminAsUserUnreadTicketIds.set(unreadTicketNumber['adminAsUserticketIds']);
        }));
        this.subscriptions.push(this.ticketService.getUnreadTicketsData().subscribe((unreadTicketNumber: any) => {
            this.unreadTicketIds.set(unreadTicketNumber['ticketIds']);
        }));
    }

    openTicket(ticket: Ticket) {
        this.isAddingTicket.set(false);
        this.selectedTicket.set(ticket);
        this.ticket.get('subject')?.setValue(ticket.subject);
        this.ticket.get('content')?.setValue('');
        this.showTicket.set(true);
    }

    addTicket() {
        this.isAddingTicket.set(true);
        this.selectedTicket.set(ticketInitialization);
        this.ticket.get('subject')?.setValue('');
        this.ticket.get('subject')?.enable();
        this.ticket.get('content')?.setValue('');
        this.showTicket.set(true);
    }

    hideTicketModal() {
        this.showTicket.set(false);
    }

    hideAttachmentsModal() {
        this.showAttachments.set(false);
    }

    resolved(captchaResponse: string) {
        this.recaptcha.setValue(captchaResponse);
    }


    onImagesUploaded(imagePreviews: { url: string }[]) {
        this.imagePreviews.set(imagePreviews);
    }

    deletePicture(imageUrl: string | ArrayBuffer) {
        this.imagePreviews.update(prevImages => prevImages.filter(img => img.url != imageUrl));
    }

    selectSortByColumn(sort: any) {
        this.handleSorting(sort);
        this.fetchTickets$(this.selectedPage());
    }

    onShowSortIcon(sortKey: string) {
        this.hoveredSortIcon.set(sortKey);
    }

    onHideSortIcon() {
        this.hoveredSortIcon.set('');
    }

    more(page: number) {
        return ((page == (this.pagination.current + 2)) || (page == (this.pagination.current - 2))) && page != 0 && (page != (this.pagination.count - 1));
    }

    get page_count() {
        return [...Array(this.pagination.count).keys()];
    }

    get first_el() {
        return (this.pagination.current * this.pagination.pageSize) + 1;
    }

    get last_el() {
        const last = (this.pagination.current + 1) * this.pagination.pageSize;
        return last > this.pagination.all ? this.pagination.all : last;
    }

    paginate(page: number = 0) {
        this.searchService.search.next({ filter: this.searchService.filters, page });
    }

    sendMessage() {
        this.spinnerService.globalSpinnerSubject.next(true);
        const requestData = {
            content: this.ticket.get('content')?.value,
            subject: this.ticket.get('subject')?.value,
            attachments: this.imagePreviews(),
            isAdminPath: this.isAdminPath(),
        };

        if (this.isAddingTicket()) {
            this.subscriptions.push(this.ticketService.addNewTicket$(requestData)
                .pipe(catchError(err => {
                    this.messagesService.set(err.message, 'error');
                    this.spinnerService.globalSpinnerSubject.next(false);
                    this.isError.set(true);
                    this.showTicket.set(false);
                    return of();
                }))
                .subscribe((data: any) => {
                    this.fetchTickets$(this.selectedPage(), data.message);
                }));
        } else {
            this.subscriptions.push(this.ticketService.addMessageToTicket$(this.selectedTicket().id, requestData)
                .pipe(catchError(err => {
                    this.messagesService.set(err.message, 'error');
                    this.spinnerService.globalSpinnerSubject.next(false);
                    this.isError.set(true);
                    this.showTicket.set(false);
                    return of();
                }))
                .subscribe((data: any) => {
                    this.ticketService.setUnreadTicketsData(data.unread_tickets);
                    this.ticketService.setAdminAsUserUnreadTicketsData(data.admin_as_user_unread_tickets);
                    this.fetchTickets$(this.selectedPage(), data.message);
                }));
        }
    }

    closeTicket(id?: number) {
        this.spinnerService.globalSpinnerSubject.next(true);
        this.subscriptions.push(this.ticketService.closeTicket$(id, this.isAdminPath())
            .pipe(catchError(err => {
                this.messagesService.set(err.message, 'error');
                this.spinnerService.globalSpinnerSubject.next(false);
                this.isError.set(true);
                return of();
            }))
            .subscribe((data: any) => {
                this.ticketService.setUnreadTicketsData(data.unread_tickets);
                console.log('admin_as_user_unread_tickets: ', data)
                this.ticketService.setAdminAsUserUnreadTicketsData(data.admin_as_user_unread_tickets);
                this.fetchTickets$(this.selectedPage(), data.message);
            }));
    }

    showAttachmentsModal(message: Message) {
        this.isAttachLoading.set(true);
        this.showAttachments.set(true);
        this.subscriptions.push(this.ticketService.showMessageAttachments$(message.id!)
            .pipe(
                catchError(err => {
                    this.messagesService.set(err.message, 'error');
                    this.isAttachLoading.set(false);
                    this.isAttachError.set(true);
                    this.showAttachments.set(true);
                    return of();
                })
            )
            .subscribe((data: any) => {
                this.isAttachLoading.set(false);
                this.isAttachError.set(false);
                this.attachments.set(data.attachments);
            }));
    }

    handleSorting({ sortKey, sortDirection }: any) {
        const newSortDirection = sortDirection == SortDirectionEnum.DESCENDANT ? SortDirectionEnum.ASCENDANT : SortDirectionEnum.DESCENDANT;
        this.sortBy.set(sortKey);
        this.tableHeaders.update(prevHeaders => prevHeaders.map(h => h.sortKey == sortKey ? { ...h, sortDirection: newSortDirection } : { ...h }));
        this.sortService.setSortConfig(sortKey, newSortDirection);
    }

    fetchTickets$(page = 0, message?: string) {
        this.spinnerService.globalSpinnerSubject.next(true);
        this.isTicketsLoading.set(true);
        return this.subscriptions.push(this.ticketService.getTicketsByPage$(page, this.isAdminPath())
            .pipe(catchError(err => {
                this.messagesService.set(err.message, 'error');
                this.spinnerService.globalSpinnerSubject.next(false);
                this.isTicketsLoading.set(false);
                this.showTicket.set(false);
                this.isError.set(true);
                return of();
            }))
            .subscribe((responseData: any) => {
                const { tickets, pagination } = responseData;
                this.tickets.set(tickets);
                this.ticketsToShow.set(tickets);
                this.pagination = pagination;
                this.pagination_showing_list = [
                    0,
                    1,
                    pagination.current - 1,
                    pagination.current,
                    pagination.current + 1,
                    pagination.count - 1,
                    pagination.count
                ];
                this.isError.set(false);
                this.spinnerService.globalSpinnerSubject.next(false);
                this.isTicketsLoading.set(false);
                this.showTicket.set(false);
                this.imagePreviews.set([]);
                if (message) {
                    this.messagesService.set(message, 'green');
                }
            }));
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(s => s.unsubscribe());
    }
}
