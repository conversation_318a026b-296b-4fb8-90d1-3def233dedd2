<div class="main">
    <div class="tickets-container h-100 w-100 d-flex flex-column">
        <div class="header">
            <button class="btn-back" [routerLink]="[isAdminPath() ? '/admin' : '/']">
                <img src="assets/icons/arrow-to-left.svg" alt="back button">
                Back
            </button>
            <div class="d-flex align-items-center title">
                <img src="assets/icons/message-icon.svg" width="30" alt="Support Icon">
                <p class="m-0 pt-1">{{isAdminPath() ? 'Admin' : 'User'}} Support</p>
            </div>
            <div class="search">
                <img class="" src="assets/icons/search-icon.svg" alt="search icon" width="10px">
                <input type="search" placeholder="Search Ticket" [formControl]="ticketToSearch">
            </div>
        </div>
        <div class="flex-grow-1 d-flex flex-column mx-auto w-100 justify-content-start"
            style="height: calc(95vh - 145px);">
            <div class="flex-grow-1 table px-3">
                <table class="table listing-table">
                    <thead class="table-dark sticky">
                        <tr>
                            <th *ngFor="let tableHeader of tableHeaders()" (click)="selectSortByColumn(tableHeader)"
                                (mouseenter)="onShowSortIcon(tableHeader.sortKey)" (mouseleave)="onHideSortIcon()"
                                style="cursor: pointer;">
                                <img src="assets/icons/sort-btn.svg" alt="sort button"
                                    [style.opacity]="sortBy() == tableHeader.sortKey ? 1 : (hoveredSortIcon() == tableHeader.sortKey) ? 0.7 : 0">
                                {{ tableHeader.header }}
                            </th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody *ngIf="ticketsToShow().length && !isError()">
                        <tr *ngFor="let ticket of ticketsToShow()"
                            [ngClass]="{'unread': (!isAdminPath() && isAdmin() ? adminAsUserUnreadTicketIds() : unreadTicketIds()).includes(ticket.id!)}">
                            <td>
                                <span>{{ ticket?.ref | empty }}</span>
                            </td>
                            <td>
                                <span>{{ ticket?.subject | empty }}</span>
                            </td>
                            <td *ngIf="isAdmin()">
                                <span>{{ ticket?.user_name | empty }}</span>
                            </td>
                            <td>
                                <span>{{ ticket?.created_at | empty }}</span>
                            </td>
                            <td>
                                <span>{{ ticket?.closed_at | empty }}</span>
                            </td>
                            <td>
                                <img src="assets/icons/{{ ticket.closed_at ? 'enabled' : 'disabled' }}-icon.svg"
                                    alt="enabled icon" width="30px">
                            </td>
                            <td class="d-flex justify-content-center">
                                <button class="mini-btn-gray py-0 px-3 m-2" (click)="openTicket(ticket)">
                                    Open
                                </button>
                                <br>
                                <button class="mini-btn-green mini-btn-gray py-0 px-3 m-2" [disabled]="ticket.closed_at"
                                    (click)="closeTicket(ticket.id)">
                                    Close
                                </button>
                            </td>
                        </tr>
                    </tbody>
                    <h1 class="w-100 text-center pt-3" *ngIf="!ticketsToShow().length && !isTicketsLoading()">{{ 'Tickets list is empty' |
                        translate }}</h1>
                    <app-spinner message *ngIf="isTicketsLoading()"></app-spinner>
                </table>
            </div>
            <div class="d-flex align-items-center justify-content-between px-4">
                <div class="p-2" *ngIf="!isAdminPath()">
                    <button class="btn btn-primary px-3 rounded-40" (click)="addTicket()">
                        <img class="p-1 pe-2" src="assets/icons/plus-icon.svg" alt="plus icon">
                        {{ 'Add a Ticket' | translate }}
                    </button>
                </div>
                <div class="pagination ms-auto me-auto" *ngIf="tickets().length">
                    <img src="assets/icons/pagination-previous.svg" alt="pagination previous icon">
                    <ng-container *ngFor="let page of page_count">
                        <button *ngIf="pagination_showing_list.includes(page) || more(page)" (click)="paginate(page)"
                            [disabled]="more(page) || page_count.length == 1"
                            [ngClass]="{active: page == pagination.current && page_count.length > 1, more: more(page) }">
                            <b *ngIf="more(page)">...</b>
                            <b *ngIf="!more(page)">{{ page + 1 }}</b>
                        </button>
                    </ng-container>
                    <img src="assets/icons/pagination-next.svg" alt="pagination next icon"
                        [ngClass]="{disabled: pagination.current == pagination.count - 1}"
                        (click)="paginate(pagination.current +1)">
                </div>
                <p class="shown mb-0" *ngIf="tickets().length">{{ 'PAGINATION.CONTENT' | translate:{first_el, last_el,
                    'all_el': pagination.all}
                    }}</p>
            </div>
        </div>
    </div>
</div>

<app-popup-ui (cancel)="hideTicketModal()" *ngIf="showTicket()" [maxWidth]="75" id="tickets-container">
    <h1 title class="text-center text-primary">Ticket</h1>
    <div message [formGroup]="ticket" style="height: 50vh;" class="w-100 d-flex flex-column align-items-center">
        <div class="d-flex align-items-center justify-content-center">
            <div class="dm-badge me-3 w-50">
                <p>{{'Ticket Number' | translate }}</p>
                <p>{{selectedTicket().ref ? selectedTicket().ref : '#AUTO_GEN'}}</p>
            </div>
            <app-field name="Object" class="w-50">
                <input autocomplete="off" formControlName="subject" class="dm-text-input" type="text" name="subject"
                    id="subject">
            </app-field>
        </div>
        <div class="conversation my-4 px-3 py-4 h-100 w-100 overflow-auto">
            <div class="conversation-message mb-2 d-flex" *ngFor="let message of selectedTicket().messages">
                <div class="sender">{{message.sender}}</div>
                <div class="me-4 separator"></div>
                <div class="message-content">
                    {{message.content}}
                    <img *ngIf="!!message?.attachments_count" class="ms-3" style="cursor: pointer;"
                        src="assets/icons/attachment-icon-filled.svg" alt="attachment" width="25"
                        (click)="showAttachmentsModal(message)" />
                </div>
            </div>
        </div>
        <div class="note" *ngIf="!selectedTicket().closed_at">
            <textarea formControlName="content" placeholder="Message"></textarea>
        </div>
        <div class="d-flex align-items-center uploaded-images-container flex-wrap">
            <div *ngFor="let imagePreview of imagePreviews(); let i = index" class="me-2">
                <div *ngIf="imagePreview.url" class="position-relative">
                    <img [src]="imagePreview.url" alt="Uploaded Image" width="50" />
                    <button class="btn btn-delete pointer position-absolute top-0 end-0 p-1"
                        (click)="deletePicture(imagePreview.url)">
                        <img src="assets/icons/blank-delete-icon.svg" alt="delete icon">
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div actions class="d-flex justify-content-center align-items-center">
        <app-image-upload (uploadImages)="onImagesUploaded($event)" [imagePreviews]="imagePreviews()"
            [addAttachmentsBtnEnabled]="!selectedTicket().closed_at"></app-image-upload>
        <button class="btn btn-primary px-4 me-3" type="button" [disabled]="!recaptcha.value || !ticket.valid"
            (click)="sendMessage()">
            {{ 'Send' | translate }}
        </button>
        <button class="btn btn-primary px-4 me-3" type="button" *ngIf="!selectedTicket().closed_at && !isAddingTicket()"
            [disabled]="!recaptcha.value" (click)="closeTicket(selectedTicket().id)">
            {{ 'Close' | translate }}
        </button>
        <div class="mt-3 mb-2 ms-5 position-relative" style="top: -10px;" *ngIf="!selectedTicket().closed_at">
            <re-captcha siteKey="{{siteKey}}" (resolved)="resolved($event)">
            </re-captcha>
        </div>
    </div>
</app-popup-ui>

<app-popup-ui (cancel)="hideAttachmentsModal()" *ngIf="showAttachments()" [maxWidth]="50" id="attachments-container">
    <div message
        [class]="'w-100 h-100 d-flex align-items-center flex-wrap ' + (attachments().length > 1 ? 'justify-content-between' : 'justify-content-center')"
        *ngIf="!isAttachLoading() && !isAttachError()">
        <img *ngFor="let attachment of attachments()" class="p-2" [src]="apiUrl +  attachment.filePath" alt="attachment"
            [ngStyle]="{'width': attachments().length > 1 ? '150px' : 'auto', 'max-width.px': 400}" />
    </div>
    <app-spinner message *ngIf="isAttachLoading() && !isAttachError()"></app-spinner>
</app-popup-ui>