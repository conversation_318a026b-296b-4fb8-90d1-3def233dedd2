@import "../../../styles/variables";
$green: #4AEAA6;

.tickets-container {
    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        position: relative;
        background-color: #fff;

        &>.title {
            img {
                padding-right: 0.7rem;
            }

            p {
                font-weight: 700;
                font-size: 1.4rem;
            }
        }

        &>.btn-back {
            background-color: $dark;
            color: white;
            border-radius: 2rem;
            border: 0;
            outline: 0;
            padding: 0.2rem 0.5rem;
            font-size: 1rem;

            img {
                padding-right: 0.3rem;
                width: 1.5rem;
            }
        }

        &>.search {
            padding: 0.2rem 1rem;
            border-radius: 3rem;
            border: 1px solid $primary;
            background-color: none;

            input {
                color: $dark;
                outline: none;
                font-weight: 500;
                font-size: 0.9rem;
                line-height: 12px;
                border: none;
                padding-left: 8px;
                color: $primary;
            }

            img {
                fill: $primary;
            }
        }
    }

    .table {
        // height: 0;
        overflow-y: auto;
        background-color: #fff;
    }

    .listing-table {
        &> :not(caption)>*>* {
            padding: 1rem 1rem;
            background-color: unset;
            border-bottom-width: 0;
        }

        thead tr {
            &>th {
                font-size: 0.8rem;
                font-weight: 500;
                background-color: $dark;
                position: relative;
                text-align: center;
            }
        }

        tr:first-of-type th {
            &:first-of-type {
                border-top-left-radius: 5px;
            }

            &:last-of-type {
                border-top-right-radius: 5px;
            }
        }

        tbody>tr {
            border-bottom-width: 0;

            &:nth-child(2n+1) {
                background-color: rgba($dark , 0.03);
            }

            &:hover {
                background-color: rgba($dark , 0.05);
            }

            &.active {
                background-color: rgba($primary, 0.15);
            }

            td {
                height: 65px;
                max-height: 65px;
                position: relative;
                vertical-align: middle;
                text-align: center;

                * {
                    font-size: 0.9rem;
                    font-weight: 500;

                    &.ref {
                        text-transform: uppercase;
                        font-size: 0.7rem;
                        padding-top: 0.3rem;
                    }
                }

                .switch {
                    position: relative;
                    display: inline-block;
                    width: 50px;
                    height: 25px;

                    .slider {
                        position: absolute;
                        cursor: pointer;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        -webkit-transition: .4s;
                        transition: .4s;

                        &::before {
                            position: absolute;
                            content: "";
                            height: 20px;
                            width: 20px;
                            left: 2px;
                            top: 2px;
                            bottom: 2px;
                            background-color: white;
                            -webkit-transition: .4s;
                            transition: .4s;
                        }

                        input {
                            opacity: 0;
                            width: 0;
                            height: 0;
                        }
                    }

                    &.disabled {
                        pointer-events: auto;
                    }

                }

                input:checked+.slider {
                    background-color: $green;
                }

                input:focus+.slider {
                    box-shadow: 0 0 1px $green;
                }

                input:checked+.slider:before {
                    -webkit-transform: translateX(26px);
                    -ms-transform: translateX(26px);
                    transform: translateX(26px);
                }

                .slider.round {
                    border-radius: 34px;
                }

                .slider.round:before {
                    border-radius: 50%;
                }


                .cut-border-bottom {
                    position: relative;
                }

                .cut-border-bottom::before {
                    content: "";
                    position: absolute;
                    bottom: 0;
                    left: 10%;
                    right: 10%;
                    height: 0.07rem;
                    top: 1rem;
                    background-color: rgba(94, 94, 94, 0.2);
                }

                span {
                    display: block;
                    padding: 0.15rem 0.5rem 0 0.5rem;
                    border-radius: 1rem;
                    width: fit-content;
                    min-width: 4rem;
                    text-align: center;
                    margin: auto;
                    color: $dark;

                    &.slider {
                        min-width: 3.5rem;
                    }
                }

                &:nth-child(1) {
                    span {
                        background-color: #595C5F;
                        color: white;
                    }
                }

                .mini-btn-green {
                    background-color: $green;
                    color: white;

                    &:disabled {
                        background-color: rgba(94, 94, 94, 0.2);
                    }
                }
            }
        }

        .unread {
            background-color: rgba($primary, 0.15);
            animation: flashBackground 2s infinite;
        }
    }

    .sticky {
        position: sticky;
        top: 0;
        bottom: 0;
        z-index: 10;
    }

    .pagination {
        display: flex;
        align-items: center;
        justify-content: center;

        &>* {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4rem;
            background-color: $secondary;
            padding: 0.3rem 0 0.1rem 0;
            margin: 0.5rem;
            width: 2rem;
            height: 2rem;
            // color: rgba($dark , 0.75);
            font-weight: 700;
            line-height: 10px;
            cursor: default;
            border: 0;
            outline: none;

            &:focus {
                border: 0;
                outline: none;
            }

            &.more {
                &>b {
                    cursor: default;
                    padding-bottom: 0.5rem;
                }
            }

            &.disabled {
                pointer-events: none;
                cursor: not-allowed;
            }

            &.active {
                background-color: $primary;
                color: white;
            }

            &:hover {
                background-color: rgba($primary, 0.5);
                transform: scale(1.1);
            }
        }

        &>img {
            padding: 0.5rem;
        }
    }
}

::ng-deep {
    app-popup-ui#tickets-container {
        h1 {
            font-size: 40px;
        }

        .popup-container>div {
            width: 60%;

            .message {
                max-width: 100%;

                &>div:first-of-type>div {
                    width: 100%;
                }

                .field-container div:first-of-type {
                    width: auto;
                }
            }

            .actions {
                button {
                    max-height: 38px;

                    &:first-of-type {
                        width: 150px;
                    }
                }
            }
        }

        .conversation {
            border: 1px solid $secondary;
            border-radius: 5px;

            .sender {
                width: 110px;
                display: block;
                font-size: 16px;
                line-height: 19.2px;
            }

            .separator {
                height: 25px;
                max-width: 1px;
                background-color: rgba($dark , 0.2);
                flex: 1;
            }

            .message-content {
                flex: 5;
                font-size: 12px;
                line-height: 14.4px;
            }
        }

        .uploaded-images-container div {
            .btn-delete {
                display: none;
            }

            &:hover .btn-delete {
                display: inline-block;
                background-color: rgba($color: $danger, $alpha: .8);
                width: 20px;
                height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 100%;

                img {
                    width: 12px;
                    height: 12px;
                    position: relative;
                    left: -1px;
                }
            }
        }

        .note {
            padding: 0;
            border: none;
        }
    }

    app-popup-ui#attachments-container .popup-container>div {
        width: 100%;
        height: 50%;
        padding: 30px;

        .message {
            height: 100%;
            overflow-y: auto;
        }
    }
}

@keyframes flashBackground {
    0% {
        background-color: rgba($primary, 0.1);
    }

    50% {
        background-color: rgba($primary, 0.3);
    }

    100% {
        background-color: rgba($primary, 0.1);
    }
}