<div class="full-body glossary-container">
    <div class="w-100 h-100 d-flex flex-column p-2 admin-container ">
        <div class="header">
            <button class="btn-back" [routerLink]="['/admin']">
                <img src="assets/icons/arrow-to-left.svg" alt="back button">
                Back
            </button>
            <div class="d-flex align-items-center title">
                <img src="assets/icons/i18n-icon.svg" width="30" alt="Company Icon">
                <p class="m-0 pt-1">Glossary</p>
            </div>
            <div class="search">
                <img class="" src="assets/icons/search-icon.svg" alt="search icon" width="10px">
                <input type="search" [placeholder]="'Search Term' | translate" [formControl]="termToSearch"
                    (keydown.enter)="navigate('next')">
                <img src="assets/icons/vertical-arrow-icon.svg" style="cursor: pointer;" alt="search-prev"
                    class="hide-icon ms-3" width="15px" (click)="navigate('prev')">
                <img src="assets/icons/vertical-arrow-icon.svg" style="transform: rotate(180deg); cursor: pointer;"
                    alt="search-next" class="hide-icon ms-3" width="15px" (click)="navigate('next')">
            </div>
            <button class="btn btn-primary px-3 rounded-40 border-0" (click)="showAddTermsField()">{{"Add Terms Group" | translate}}</button>
            <button class="btn btn-primary px-3 rounded-40 border-0" (click)="showLanguageNameField()"
                [disabled]="addLanguageBtnDisabled()">{{"Add Language" | translate}}</button>
        </div>
        <div class="group-container">
            <div *ngIf="showCustomLangForm()" class="d-flex align-items-center justify-content-between w-50">
                <app-field name="New Language Name" style="width: 400px;">
                    <input autocomplete="off" [formControl]="customLanguage" class="dm-text-input my-0" type="text"
                        name="language" id="language">
                </app-field>
                <button class="btn btn-primary px-5 mx-3 rounded-40 border-0" (click)="addNewLanguage()">{{ 'Add' | translate }}</button>
                <button class="btn btn-primary px-5 rounded-40 border-0" (click)="cancel()">{{ 'COMMON.CANCEL_BTN' | translate }}</button>
            </div>
            <div class="container d-flex align-items-center mw-100">
                <button class="btn border-0" (click)="prev()" [disabled]="!translation()">
                    <img src="assets/icons/arrow-down.svg" alt="down-icon" class="slider-arrow" [width]="40"
                        style="transform: rotate(90deg);">
                </button>
                <form [formGroup]="translationForm" (keydown.enter)="$event.preventDefault()"
                    (click)="$event.preventDefault()" (ngSubmit)="onSubmit()" #tableView
                    [ngStyle]="{height: showCustomLangForm() ? 'calc(100vh - 340px)': 'calc(100vh - 290px)'}">
                    <table class="table listing-table" [style.transform]="'translateX(' + translation() + 'px)'">
                        <thead class="table-dark sticky">
                            <tr>
                                <th>Group Name</th>
                                <th *ngFor="let language of languagesName()">
                                    {{ language | capitalize }}
                                </th>
                            </tr>
                        </thead>
                        <tbody formGroupName="groups">
                            <tr *ngFor="let group of getGroups?.controls; let i = index" [formGroupName]="i" #groupRef>
                                <ng-container formGroupName="languages">
                                    <td class="text-start">
                                        <img src="assets/icons/arrow-down.svg" alt="down-icon" class="hide-icon"
                                            [@rotateIcon]="group.get('isExpanded')?.value ? 'rotated' : 'normal'"
                                            [width]="20" (click)="toggleGroupExpand(i)">
                                        {{ group.get('groupName')?.value | capitalize | empty }}
                                    </td>
                                    <td *ngFor="let language of getLanguages(group)?.controls; let j = index"
                                        [formGroupName]="j" class="language">
                                        <ng-container formGroupName="terms">
                                            <tr *ngFor="let term of getTerms(language)?.controls; let k = index"
                                                [ngStyle]="{display: group.get('isExpanded')?.value ? 'flex': 'none'}"
                                                [formGroupName]="k">
                                                <td class="w-100 p-0" *ngIf="term.get('isVisible')?.value">
                                                    <textarea
                                                        [ngStyle]="{'background-color': 
                                                                        termToSearch.value && term.get('term')?.value.toLowerCase().includes(termToSearch.value) && 
                                                                        termToSearch.value.length ? highlightingColor : '#f0f0f0'}"
                                                        formControlName="term" class="border-0 w-100 h-100 term"></textarea>
                                                </td>
                                            </tr>
                                        </ng-container>
                                    </td>
                                </ng-container>
                            </tr>
                        </tbody>
                    </table>
                    <h1 class="w-100 text-center pt-3" *ngIf="!getGroups?.controls?.length && !languagesOnLoading()">
                        {{"Terms List is Empty" | translate}}
                    </h1>
                    <app-spinner-html spinner *ngIf="languagesOnLoading()"></app-spinner-html>
                </form>
                <button class="btn border-0" [disabled]="remainingRightWidth() <= 0" (click)="next()">
                    <img src="assets/icons/arrow-down.svg" alt="down-icon" class="slider-arrow" [width]="40"
                        style="transform: rotate(-90deg);">
                </button>
            </div>
            <button class="btn btn-primary px-3 rounded-40" type="submit" (click)="onSubmit()"
                [disabled]="!customLanguage.valid && !isEditingLanguages()">{{ 'Submit' | translate }}</button>
        </div>
    </div>
</div>

<app-popup-ui (cancel)="cancelANT()" *ngIf="showAddTermsForm()" [maxWidth]="80">
    <h4 title class="text-center text-primary">{{"Add New Terms" | translate}}</h4>
    <form message class="row mx-4" (ngSubmit)="onANT()" [formGroup]="ANTForm" style="overflow-x: hidden;" #ANTView>
        <!-- <app-field name="Group Name" style="width: 400px;">
            <input autocomplete="off" formControlName="groupName" class="dm-text-input my-0" type="text"
                name="groupName" id="groupName">
        </app-field> -->
        <div class="d-flex align-items-center justify-content-between">
            <button class="btn border-0" type="button" (click)="ANTModalPrev()" [disabled]="!ANTTranslation()">
                <img src="assets/icons/arrow-down.svg" alt="down-icon" class="slider-arrow" [width]="40"
                    style="transform: rotate(90deg); cursor: pointer;">
            </button>
            <button class="btn border-0" type="button" (click)="ANTModalNext()" [disabled]="remainingANTRightWidth() <= 0">
                <img src="assets/icons/arrow-down.svg" alt="down-icon" class="slider-arrow" [width]="40"
                    style="transform: rotate(-90deg); cursor: pointer;">
            </button>
        </div>
        <table class="table listing-table" [style.transform]="'translateX(' + ANTTranslation() + 'px)'">
            <thead class="table-dark sticky">
                <tr>
                    <th *ngIf="isDeleteTermVisible()" style="width: 60px;">Actions</th>
                    <th *ngFor="let language of languagesName()" style="width: 300px;">
                        {{ language | capitalize }}
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td *ngIf="isDeleteTermVisible()">
                        <tr *ngFor="let term of getTerms(getLanguages(ANTForm).at(0))?.controls; let k = index" class="d-flex justify-content-center align-items-center">
                            <td class="border-0 d-flex align-items-center" style="height: 38px; min-height: 38px">
                                <button class="btn btn-delete pointer my-0" (click)="deleteTerm(k)">
                                    <img src="assets/icons/delete-icon.svg" alt="delete icon" width="100%">
                                </button>
                            </td>
                        </tr>
                    </td>
                    <ng-container formGroupName="languages">
                        <td *ngFor="let language of getLanguages(ANTForm)?.controls; let j = index" [formGroupName]="j"
                            class="language">
                            <ng-container formGroupName="terms">
                                <tr *ngFor="let term of getTerms(language)?.controls; let k = index"
                                    class="d-flex"
                                    [formGroupName]="k">
                                    <td class="w-100 p-0">
                                        <textarea formControlName="term" class="border-0 w-100 h-100 term" [placeholder]="language.get('languageName')?.value + ' term'"></textarea>
                                    </td>
                                </tr>
                            </ng-container>
                        </td>
                    </ng-container>
                </tr>
            </tbody>
        </table>
        <div class="d-flex justify-content-center mt-5">
            <button class="btn btn-primary" type="submit" [disabled]="!ANTForm.valid">{{ 'COMMON.SAVE_BTN' | translate }}</button>
            <button class="btn btn-primary px-4" type="button" (click)="addNewTerm()">
              {{ "Add Term" | translate }}
            </button>
            <button class="btn btn-primary px-4" type="button" (click)="showDeleteTerm()">
              {{ "Delete Term" | translate }}
            </button>
            <button class="btn btn-primary" (click)="cancelANT()">{{ 'COMMON.CANCEL_BTN' | translate }}</button>
          </div>
    </form>
</app-popup-ui>
