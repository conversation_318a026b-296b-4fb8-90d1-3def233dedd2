import { trigger, state, style, transition, animate } from '@angular/animations';
import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, inject, OnDestroy, OnInit, QueryList, signal, ViewChild, ViewChildren } from '@angular/core';
import { AbstractControl, FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { of, Subscription } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { CustomTranslateLoader } from 'src/app/services/custom-translate-loader.service';
import { CustomTranslateService } from 'src/app/services/custom-translate-service.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';

@Component({
    selector: 'app-admin-glossary',
    templateUrl: './glossary.component.html',
    styleUrls: ['./glossary.component.scss'],
    animations: [
        trigger('rotateIcon', [
            state('normal', style({ transform: 'rotate(0deg)' })),
            state('rotated', style({ transform: 'rotate(180deg)' })),
            transition('normal <=> rotated', animate('100ms ease-in-out')),
        ]),
    ]
})

export class GlossaryComponent implements OnInit, OnDestroy {
    // ANT stands for AddNewTerms
    @ViewChild('tableView') tableView!: ElementRef;
    @ViewChild('ANTView') ANTView!: ElementRef;
    @ViewChildren('groupRef') groupRefs!: QueryList<ElementRef>;
    // Forms
    termToSearch: FormControl;
    newGroupName: FormControl;
    translationForm: FormGroup;
    ANTForm: FormGroup;
    // Services
    private http = inject(HttpClient);
    private spinner = inject(SpinnerService);
    private alert = inject(MessagesService);
    private customTranslateLoader = inject(CustomTranslateLoader);
    private customTranslateService = inject(CustomTranslateService);
    private translateService = inject(TranslateService);
    // Signals
    showCustomLangForm = signal<boolean>(false);
    addLanguageBtnDisabled = signal<boolean>(false);
    isAddingLanguage = signal<boolean>(false);
    isEditingLanguages = signal<boolean>(false);
    languagesOnLoading = signal<boolean>(false);
    isDeleteTermVisible = signal<boolean>(false);
    translation = signal<number>(0);
    ANTTranslation = signal<number>(0);
    tableWidth = signal<number>(0);
    ANTTableWidth = signal<number>(0);
    viewWidth = signal<number>(0);
    ANTViewWidth = signal<number>(0);
    remainingRightWidth = signal<number>(0);
    remainingANTRightWidth = signal<number>(0);
    groupNames = signal<any>([]);
    languages = signal<{ name: string; slug: string }[]>([]);
    languagesName = signal<any>([]);
    modifiedLanguages = signal<{ languageName: string; languageInd: number }[]>([]);
    currentIndex = signal<{ group: number, language: number, term: number }>({ group: -1, language: -1, term: -1 });
    matchedTerms: { groupIndex: number, languageIndex: number, termIndex: number }[] = [];
    currentMatchIndex: number = -1;
    selectedLanguage: any = null;
    // Constants
    highlightingColor = '#fffdaf';
    // Form Controls
    customLanguage = new FormControl('', [Validators.required]);
    // Subscriptions
    subscriptions: Subscription[] = [];
    showAddTermsForm = signal(false);

    ngAfterViewInit() {
        this.viewWidth.set(this.tableView.nativeElement.offsetWidth);
    }

    ngOnInit(): void {
        this.customTranslateService.getLanguagesName().subscribe(languages => {
            this.languages.set(languages);
            this.languagesName.set(languages.map(lang => lang.name));
        });
        this.loadAllLanguages$();
        this.termToSearch = new FormControl('');
        this.newGroupName = new FormControl('');

        this.translationForm = new FormGroup({
            groups: new FormArray([])
        });

        this.ANTForm = this.createGroup();

        this.termToSearch.valueChanges.subscribe(() => {
            this.matchedTerms = [];
            const searchValue = this.termToSearch.value.toLowerCase();

            this.getGroups.controls.forEach((group, i) => {
                this.getLanguages(group).controls.forEach((language, j) => {
                    const termsArray = this.getTerms(language);

                    termsArray.controls.forEach((termForm, k) => {
                        const termValue = termForm.get('term')?.value.toLowerCase();
                        if (termValue.includes(searchValue)) {
                            this.matchedTerms.push({ groupIndex: i, languageIndex: j, termIndex: k });
                        }
                    });
                });

                if (this.matchedTerms.length > 0) {
                    this.currentMatchIndex = 0;
                    this.scrollToCurrent();
                }
            });

        })
    }

    scrollToCurrent() {
        if (this.matchedTerms.length > 0 && this.currentMatchIndex !== -1) {
            const currentMatch = this.matchedTerms[this.currentMatchIndex];
            const groupElement = this.groupRefs.toArray()[currentMatch.groupIndex].nativeElement;
            const languageElement = groupElement.querySelectorAll('.language')[currentMatch.languageIndex];
            const termElement = languageElement.querySelectorAll('.term')[currentMatch.termIndex];

            if (termElement) {
                termElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                });
            }
        }
    }

    navigate(direction: 'prev' | 'next') {
        if (this.matchedTerms.length > 0) {
            if (direction === 'next') {
                this.currentMatchIndex = (this.currentMatchIndex + 1) % this.matchedTerms.length;
            } else if (direction === 'prev') {
                this.currentMatchIndex = (this.currentMatchIndex - 1 + this.matchedTerms.length) % this.matchedTerms.length;
            }
            this.scrollToCurrent();
        }
    }

    get getGroups(): FormArray {
        return this.translationForm?.get('groups') as FormArray;
    }

    getLanguages(group: any) {
        return group.get('languages') as FormArray;
    }

    getTerms(language: any) {
        return language.get('terms') as FormArray;
    }

    listenToAllTermsChanges() {
        this.getGroups?.controls.forEach(group => {
            const languagesArray = this.getLanguages(group);
            languagesArray?.controls.forEach((language, langInd) => {
                this.listenToLanguageChanges(language, langInd)
            });
        });
    }

    listenToLanguageChanges(languageGroup: AbstractControl, languageInd: number) {
        const termsArray = this.getTerms(languageGroup);
        termsArray.valueChanges.subscribe(() => {
            this.isEditingLanguages.set(true);
            const languageName = languageGroup.get('languageName')?.value;
            this.modifiedLanguages.update(prevLanguages =>
                prevLanguages.find(lang => lang.languageName == languageName) ?
                    prevLanguages :
                    [...prevLanguages, { languageName, languageInd }]
            );
        });
    }

    next() {
        this.remainingRightWidth.set(this.translation() + this.tableWidth() - this.viewWidth())
        if (this.remainingRightWidth() < this.viewWidth()) {
            this.translation.update(prevTrans => prevTrans -= this.remainingRightWidth());
            this.remainingRightWidth.set(0);
        } else {
            this.translation.update(prevTrans => prevTrans -= this.viewWidth());
            this.remainingRightWidth.update(prevWidth => prevWidth - this.viewWidth());
        }
    }

    ANTModalNext() {
        this.remainingANTRightWidth.set(this.ANTTranslation() + this.ANTTableWidth() - this.ANTViewWidth())
        if (this.remainingANTRightWidth() < this.ANTViewWidth()) {
            this.ANTTranslation.update(prevTrans => prevTrans -= this.remainingANTRightWidth());
            this.remainingANTRightWidth.set(0);
        } else {
            this.ANTTranslation.update(prevTrans => prevTrans -= this.ANTViewWidth());
            this.remainingANTRightWidth.update(prevWidth => prevWidth - this.ANTViewWidth());
        }
    }

    prev() {
        if (this.translation() + this.viewWidth() > 0) {
            this.remainingRightWidth.set(this.tableWidth() - this.viewWidth());
            this.translation.set(0);
        } else {
            this.translation.update(prevTrans => prevTrans += this.viewWidth());
            this.remainingRightWidth.update(prevWidth => prevWidth + this.viewWidth());
        }
    }

     ANTModalPrev() {
        if (this.ANTTranslation() + this.ANTViewWidth() > 0) {
            this.remainingANTRightWidth.set(this.ANTTableWidth() - this.ANTViewWidth());
            this.ANTTranslation.set(0);
        } else {
            this.ANTTranslation.update(prevTrans => prevTrans += this.ANTViewWidth());
            this.remainingANTRightWidth.update(prevWidth => prevWidth + this.ANTViewWidth());
        }
    }

    addGroup(groupName = '') {
        const groupForm = this.createGroup(groupName);
        this.getGroups.push(groupForm);
    }

    createGroup(groupName = '') {
        return new FormGroup({
            groupName: new FormControl(groupName),
            isExpanded: new FormControl(true),
            languages: new FormArray([]),
        });
    }

    addLanguage(group: AbstractControl, languageName = '') {
        const languageForm = this.createLanguage(languageName);
        this.getLanguages(group).push(languageForm);
    }

    createLanguage(languageName = '') {
        return new FormGroup({
            languageName: new FormControl(languageName, Validators.required),
            terms: new FormArray([]),
        });
    }

    addTerm(language: AbstractControl, identifier = '', term = '') {
        const termForm = this.createTerm(identifier, term);
        this.getTerms(language).push(termForm);
    }

    createTerm(identifier = '', term = '') {
        return new FormGroup({
            identifier: new FormControl(identifier),
            term: new FormControl(term, Validators.required),
            isVisible: new FormControl(true)
        });
    }

    addNewLanguage() {
        this.showCustomLangForm.set(false);
        this.isAddingLanguage.set(true);
        this.languagesName.update(prevNames => [...prevNames, this.customLanguage.value]);
        this.setTableWidth();
        this.remainingRightWidth.update(prevRemRightWidth => prevRemRightWidth + 300);
        this.translation.update(prevTrans => prevTrans - this.remainingRightWidth());
        this.remainingRightWidth.set(0);
        this.getGroups.controls.forEach(group => {
            const languagesArray = this.getLanguages(group);
            const newLanguageForm = new FormGroup({
                languageName: new FormControl(this.customLanguage.value, Validators.required),
                terms: new FormArray([]),
            });
            const terms = this.getTerms(languagesArray.at(0)).controls;
            terms.forEach((term: any) => {
                const clonedTerm = this.cloneTerm(term);
                this.getTerms(newLanguageForm).push(clonedTerm);
            });
            languagesArray.push(newLanguageForm);
        });
        this.listenToAllTermsChanges();
    }

    cloneTerm(term: FormGroup): FormGroup {
        const clonedTerm = new FormGroup({
            identifier: new FormControl(term.get('identifier')?.value),
            term: new FormControl(term.get('term')?.value),
            isVisible: new FormControl(true),
        });

        return clonedTerm;
    }

    toggleGroupExpand(groupIndex: number) {
        const expandedControl = this.getGroups?.at(groupIndex)?.get('isExpanded');
        const isGroupExpanded = expandedControl?.value;
        expandedControl?.setValue(!isGroupExpanded);
    }

    setLanguagePath(lang: string) {
        return this.http.get<any>(`/assets/i18n/${lang}.json`);
    }

    getObjectKeys(obj: any) {
        return Object.keys(obj);
    }

    getObjectValues(obj: any) {
        return typeof obj === "string" ? [obj] : Object.values(obj);
    }

    loadAllLanguages$() {
        this.spinner.globalSpinnerSubject.next(true);
        this.languagesOnLoading.set(true);
        this.subscriptions.push(this.customTranslateLoader.getAllTranslations().pipe(
            catchError(err => {
                this.spinner.globalSpinnerSubject.next(false);
                this.languagesOnLoading.set(false);
                this.alert.set(err, 'error');
                return of('');
            })
        ).subscribe((response: any) => {
            if (response.translations) {
                const translations = response.translations;
                const groupNames = this.getObjectKeys(translations[0].content);
                this.setTableWidth();
                this.remainingRightWidth.set(this.tableWidth() - this.viewWidth());
                groupNames.forEach(groupName => {
                    const newGroup = this.createGroup(groupName);
                    this.getGroups.push(newGroup);
                    translations.forEach((language: any) => {
                        const newLanguage = this.createLanguage(language.name);
                        this.getLanguages(newGroup).push(newLanguage);
                        const identifiers = this.getObjectKeys(language.content[groupName]);
                        if(typeof identifiers === "string") {
                            this.getTerms(newLanguage).push(this.createTerm(identifiers[0], language.content[groupName]));
                        } else {
                            const terms = this.getObjectValues(language.content[groupName]);
                            terms.forEach((term: any, i: number) => {
                                this.getTerms(newLanguage).push(this.createTerm(identifiers[i], term));
                            });
                        }
                    });
                });
                this.getGroups.controls.forEach(group => {
                    this.getLanguages(group).at(0).disable();
                });
                this.listenToAllTermsChanges();
                this.spinner.globalSpinnerSubject.next(false);
                this.languagesOnLoading.set(false);
            }
        }));
    }

    setTableWidth() {
        this.tableWidth.set((this.languagesName().length * 300) + 230);
    }

    setANTTableWidth() {
        this.ANTTableWidth.set((this.languagesName().length * 300));
        this.remainingANTRightWidth.set(this.ANTTableWidth() - this.ANTViewWidth());
    }

    showLanguageNameField() {
        this.showCustomLangForm.set(true);
        this.addLanguageBtnDisabled.set(true);
    }

    onSubmit() {
        if (this.isAddingLanguage()) {
            let requestData = {};
            this.getGroups.controls.forEach(group => {
                const newLanguageTerms = this.getLanguages(group).at(this.languagesName().length - 1);
                let groupTerms = {};
                (newLanguageTerms.get('terms') as FormArray).controls.forEach(term => {
                    groupTerms = {
                        ...groupTerms,
                        [term.get('identifier')?.value]: term.get('term')?.value
                    }
                });
                const groupName = group.get('groupName')?.value; 
                requestData = !!groupName ? {
                    ...requestData,
                    [groupName]: { ...groupTerms }
                } : {...groupTerms}
            });
            this.spinner.globalSpinnerSubject.next(true);
            this.subscriptions.push(this.customTranslateLoader.createNewTranslation({
                name: this.customLanguage.value,
                content: requestData
            }).pipe(
                catchError(err => {
                    this.alert.set(err?.message.length ? err?.message[0] : err?.message, 'error');
                    this.spinner.globalSpinnerSubject.next(false);
                    this.isAddingLanguage.set(false);
                    return of('');
                })
            ).subscribe((response) => {
                this.alert.set(response?.message as string, 'green');
                this.spinner.globalSpinnerSubject.next(false);
                this.addLanguageBtnDisabled.set(false);
                this.isAddingLanguage.set(false);
                this.customLanguage.setValue('');
                this.customTranslateService.setLanguagesName(response?.languages);
            }));
        }
        if (this.isEditingLanguages()) {
            let requestData: { name: string, content: any }[] = [];
            this.modifiedLanguages().map(lang => {
                let languageData = {};
                this.getGroups.controls.forEach(group => {
                    const modifiedTerms = this.getLanguages(group).at(lang.languageInd);
                    let groupTerms = {};
                    (modifiedTerms.get('terms') as FormArray).controls.forEach(term => {
                        groupTerms = {
                            ...groupTerms,
                            [term.get('identifier')?.value]: term.get('term')?.value
                        }
                    });
                    const groupName = group.get('groupName')?.value;
                    languageData = !!groupName ?  {
                        ...languageData,
                        [groupName]: { ...groupTerms }
                    } : { ...groupTerms };
                });

                requestData.push({
                    name: lang.languageName,
                    content: languageData
                });
            });

            this.spinner.globalSpinnerSubject.next(true);
            this.subscriptions.push(this.customTranslateLoader.updateLanguages(requestData).pipe(
                catchError(err => {
                    this.alert.set(err, 'error');
                    this.spinner.globalSpinnerSubject.next(false);
                    this.isEditingLanguages.set(false);
                    return of('');
                })
            ).subscribe(response => {
                this.alert.set(response?.message as string, 'green');
                this.translateService.reloadLang(this.lang);
                this.spinner.globalSpinnerSubject.next(false);
                this.isEditingLanguages.set(false);
            }));
        }
    }

    cancel() {
        this.showCustomLangForm.set(false);
        this.addLanguageBtnDisabled.set(false);
    }

    showAddTermsField() {
        this.showAddTermsForm.set(true);
        this.ANTTranslation.set(0);
        this.remainingANTRightWidth.set(0);
        setTimeout(() =>{
            this.ANTViewWidth.set(this.ANTView.nativeElement.offsetWidth);
            this.setANTTableWidth();
            this.ANTGroup();
        });
    }

    cancelANT() {
        this.showAddTermsForm.set(false);
        this.ANTForm.get('groupName')?.setValue('');
        this.getLanguages(this.ANTForm).clear();
    }

    addNewTerm() {
        const languages = this.getLanguages(this.ANTForm);
        languages.controls.forEach(lang => {
            this.getTerms(lang).push(this.createTerm());
        });
    }

    onANT() {
        this.isEditingLanguages.set(true);
        const group = this.deepCopyFormGroup(this.ANTForm);
        const languages = this.getLanguages(group).controls.map((lang, i) => ({ languageName: lang.get('languageName')?.value, languageInd: i }));
        this.modifiedLanguages.set(languages);
        this.showAddTermsForm.set(false);
        this.getGroups.insert(0, group);
        this.cancelANT();
    }

    ANTGroup() {
        this.languagesName().forEach((lang: string) => {
            const language = this.createLanguage(lang);
            this.getLanguages(this.ANTForm).push(language);
            this.getTerms(language).push(this.createTerm())
        });
    }

    showDeleteTerm() {
        this.isDeleteTermVisible.set(true);
    }

    deleteTerm(termInd: number) {
        this.getLanguages(this.ANTForm).controls.forEach(lang => {
            if (this.getTerms(lang).length > 1) {
                this.getTerms(lang).removeAt(termInd);
            }
        });
    }

    deepCopyFormGroup(group: FormGroup): FormGroup {
        const newGroup = this.createGroup(group.get('groupName')?.value);
        this.getLanguages(group).controls.forEach(language => {
            const newLanguage = this.createLanguage(language.get('languageName')?.value);
            this.getTerms(language).controls.forEach((term, i) => {
                const englishTerm = this.getTerms(this.getLanguages(group).at(0)).at(i).get('term')?.value;
                const termValue = term.get('term')?.value;
                this.getTerms(newLanguage).push(this.createTerm(englishTerm, termValue));
            });
            this.getLanguages(newGroup).push(newLanguage);
        });
        return newGroup;
    }

    get lang() {
        return localStorage.getItem('lang') || '';
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(s => s.unsubscribe());
        this.subscriptions = [];
    }
}
