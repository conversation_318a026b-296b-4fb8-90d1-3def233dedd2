@import "../../../styles/variables";

.glossary-container {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    position: relative;
    background-color: #fff;

    &>.title {
      img {
        padding-right: 0.7rem;
      }

      p {
        font-weight: 700;
        font-size: 1.4rem;
      }
    }

    &>.btn-back {
      background-color: $dark;
      color: white;
      border-radius: 2rem;
      border: 0;
      outline: 0;
      padding: 0.2rem 0.5rem;
      font-size: 1rem;

      img {
        padding-right: 0.3rem;
        width: 1.5rem;
      }
    }

    &>.search {
      padding: 0.2rem 1rem;
      border-radius: 3rem;
      border: 1px solid $primary;
      background-color: none;

      input {
        color: $dark;
        outline: none;
        font-weight: 500;
        font-size: 0.9rem;
        line-height: 12px;
        border: none;
        padding-left: 8px;
        color: $primary;
      }

      img {
        fill: $primary;
      }
    }
  }

  .group-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .container {
      margin: 20px 0;

      form {
        margin: 0 20px;
        overflow-x: hidden;
        overflow-y: scroll;
      }

      .slider-arrow {
        cursor: pointer;
      }
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th,
    td {
      padding: 8px;
      text-align: left;
    }

    input {
      text-align: center;
    }

    button {
      margin-top: 10px;
    }
  }
}

.table {
  position: relative;
  table-layout: fixed;
  transition: all 1s ease-in-out;
}

table,
th,
td {
  border: 1px solid black;
}


.listing-table {
  &> :not(caption)>*>* {
    padding: 0.5rem 0.5rem;
    background-color: unset;
    border-bottom-width: 0;

    .hiddable {
      opacity: 1;
      display: flex !important;
      transition: opacity .1s ease,
        display .1s ease allow-discrete;

      &.collapsed {
        opacity: 0;
        display: none;
      }
    }
  }

  thead {

    &.sticky {
      position: sticky;
      top: -1px;
      bottom: 0;
      z-index: 4;
    }

    tr {
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;

      &>th {
        font-size: 0.8rem;
        font-weight: 500;
        background-color: $dark;
        color: $white;
        position: relative;
        text-align: center;
        width: 230px;

        &:not(&:first-of-type) {
          width: 300px;
          min-width: 300px;
        }
      }
    }
  }

  tbody>tr {
    .centered {
      display: flex;
      flex-direction: column;
      align-items: center;
      border: 1px solid transparent;
    }

    &:nth-child(2n+1) {
      background-color: rgba($dark , 0.03);
    }

    &:hover {
      background-color: rgba($dark , 0.05);
    }

    &.active {
      background-color: rgba($primary, 0.15);
    }

    td {
      position: relative;
      text-align: center;

      * {
        font-size: 0.9rem;
        font-weight: 500;

        &.ref {
          text-transform: uppercase;
          font-size: 0.7rem;
          padding-top: 0.3rem;
        }

        textarea {
          resize: none;
          text-align: center;
          align-content: center;
          vertical-align: middle;
        }
      }

      span {
        display: block;
        background-color: rgba($dark , 0.75);
        color: white;
        padding: 0.15rem 0.5rem 0 0.5rem;
        border-radius: 1rem;
        width: fit-content;
        min-width: 4rem;
        text-align: center;
        margin: auto;
      }

      &:nth-child(2n) {
        span {
          background-color: darken($secondary , 0.5);
          color: $dark;

        }
      }

      &:nth-child(2n+1) {
        span {
          background-color: rgba($dark , 0.65);
          color: white;
        }
      }

      &:nth-child(1) {
        span {
          background-color: #FF7729;
          color: white;

        }
      }

      input {
        border: none;
      }

      .btn-delete,
      .btn-add,
      .btn-edit {
        width: 30px;
        height: 30px;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2rem;
        background-color: rgba($dark , 0.15);
        border: 0;
        transition: transform 300ms ease-in;
        margin: auto;

        &:hover {
          border: 0;
          transform: scale(1.1);
        }
      }

      .hide-icon {
        cursor: pointer;
      }
    }
  }
}