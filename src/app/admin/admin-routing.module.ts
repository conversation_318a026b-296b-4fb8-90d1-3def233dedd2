import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
// import { canActivateAdmin } from '../guards/adminGuard';
import { AdminMainComponent } from './main/main.component';
import { CompanyUsersComponent } from './users/users.component';
import { GlossaryComponent } from './glossary/glossary.component';
import { SupportComponent } from './support/support.component';
import { StandardComponent } from './standard/standard.component';
import { AdminCompanyComponent } from './company/company.component';
import { NewModeComponent } from './standard/mode/new/new.component';
import { ShoetypeComponent } from './standard/shoetype/shoetype.component';
import { ModeComponent } from './standard/mode/mode.component';

const columns = [
  { name: 'Device', slug: 'scanner_device', translationKey: 'SCANNERS.DEVICE' },
  { name: 'Scan Number', slug: 'scan_number', translationKey: 'SCANNERS.NUMBER' },
  { name: 'Name', slug: 'name', translationKey: 'IDENTIFICATION.NAME' },
  { name: 'Type', slug: 'scanner_type_name', translationKey: 'SCANNERS.TYPE' },
  { name: 'Added Date', slug: 'formatted_created_at', translationKey: 'BACKEND.Added Date' }
];

// const routes: Routes = [
//   {
//     path: '', canActivate: [ canActivateAdmin ], component: AdminMainComponent
//   },
//   {
//     path: 'companies', canActivate: [ canActivateAdmin ], component: AdminCompanyComponent
//   },
//   {
//     path: 'scanners', canActivate: [ canActivateAdmin ], component: AdminScannersComponent, data: { columns }
//   },
//   {
//     path: 'companies/:ref/users', canActivate: [ canActivateAdmin ], component: CompanyUsersComponent
//   },
//   {
//     path: 'glossary', canActivate: [ canActivateAdmin ], component: GlossaryComponent
//   },
//   {
//     path: 'support', canActivate: [ canActivateAdmin ], component: SupportComponent
//   },
// ];
const routes: Routes = [
  {
    path: '', component: AdminMainComponent
  },
  {
    path: 'companies', component: AdminCompanyComponent
  },
  {
    path: 'companies/:ref/users', component: CompanyUsersComponent
  },
  {
    path: 'glossary', component: GlossaryComponent
  },
  {
    path: 'support', component: SupportComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TemplateRoutingModule { }
