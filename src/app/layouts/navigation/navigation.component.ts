import { Component, inject, OnDestroy, signal } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { catchError, of, Subscription } from 'rxjs';
import { CustomTranslateLoader } from 'src/app/services/custom-translate-loader.service';
import { CustomTranslateService } from 'src/app/services/custom-translate-service.service';
import { TicketService } from 'src/app/services/ticket.service';
import { UserService } from 'src/app/services/user.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';

@Component({
  selector: 'app-navigation',
  templateUrl: './navigation.component.html',
  styleUrls: ['./navigation.component.scss']
})
export class NavigationComponent implements OnDestroy {
  isUserMenu: boolean = false;
  isLanguageMenuOpen: boolean = false;
  // Subscriptions
  subscriptions: Subscription[] = [];
  // Signals
  languages = signal<{ name: string, slug: string }[]>([]);
  rawLanguages = signal<{ name: string, slug: string }[]>([]);
  unreadTickets = signal<number>(0);
  isAdmin = signal<boolean>(false);
  // Services
  translateService = inject(TranslateService);
  customTranslateService = inject(CustomTranslateService);
  customTranslateLoader = inject(CustomTranslateLoader);
  spinner = inject(SpinnerService);
  alert = inject(MessagesService);
  ticketService = inject(TicketService);
  constructor(public UserService: UserService,
    public translate: TranslateService) {
    this.isAdmin.set(this.UserService.isUserAdmin);

    // this.subscriptions.push(this.customTranslateLoader.getLanguages().subscribe(response => {
    //   this.customTranslateService.setLanguagesName(response?.languages);
    // }));

    this.customTranslateService.getLanguagesName().subscribe(languages => {
      this.rawLanguages.set(languages);
      this.languages.set(languages.filter((lang: { slug: string; }) => lang.slug != this.lang));
    });

    if (this.isAdmin()) {
      this.subscriptions.push(this.ticketService.getAdminAsUserUnreadTicketsData().subscribe((unreadTicketNumber: any) => {
        this.unreadTickets.set(unreadTicketNumber['adminAsUserticketCount']);
      }));
    } else {
      this.subscriptions.push(this.ticketService.getUnreadTicketsData().subscribe((unreadTicketNumber: any) => {
        this.unreadTickets.set(unreadTicketNumber['ticketCount']);
      }));
    }
  }

  showUserMenu() {
    if (!this.isUserMenu) {
      this.isUserMenu = true
    }
  }

  hideUserMenu() {
    this.isUserMenu = false
  }

  setSelectedLangauge(lang: string) { 
    this.spinner.globalSpinnerSubject.next(true);
    this.UserService.setPreferedLanguage(lang).pipe(catchError(err => {
      this.alert.set(err.message, 'error');
      this.spinner.globalSpinnerSubject.next(false);
      return of();
    })).subscribe((data) => {
      this.customTranslateService.setLanguage(lang, data!);
      this.languages.set(this.rawLanguages().filter(language => language.slug != lang));
      this.toggleLanguageMenuModal();
    })
  }

  toggleLanguageMenuModal() {
    this.isLanguageMenuOpen = !this.isLanguageMenuOpen;
  }

  get lang() {
    return this.translateService.getDefaultLang();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
