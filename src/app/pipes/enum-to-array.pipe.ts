import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'objectToArray'
})
export class ObjectToArrayPipe implements PipeTransform {

    transform(enumObject: Object, extraOption?: string) {
        const arr = Object.entries(enumObject).map(([label, value]) => ({ label, value }));
        if (extraOption) {
            return [...arr, { label: extraOption.toLocaleLowerCase(), value: extraOption }]
        }
        return arr;
    }
}
