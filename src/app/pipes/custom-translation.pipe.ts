import { inject, Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LastInfosTranslationKey } from '../shared/enums/translations';

@Pipe({
    name: 'customTranslate',
    pure: false
})
export class CustomTranslatePipe implements PipeTransform {
    private translateService = inject(TranslateService);
    lastInfosTranslationKey = LastInfosTranslationKey;

    transform(value: any): string {
        const valueToString = '' + value;
        const termArray = valueToString.split(' / ');

        return termArray.length == 1 ?
            valueToString :
            termArray.map((term: string) => this.translateService.instant(this.lastInfosTranslationKey[term])).join(' / ');
    }
}
