import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TemplateComponent } from './template/template.component';
import { LoginComponent } from './pages/login/login.component';
import { canActivateAuth } from './guards/authGuard';
import { AdminComponent } from './admin/admin.component';
import { ResetPasswordComponent } from './pages/reset-password/reset-password.component';
// import { ResetPasswordRequestComponent } from './pages/reset-password/reset-password-request.component';

const routes: Routes = [
  {
    path: '', canActivate: [ canActivateAuth ], component: TemplateComponent,
    // path: '', component: TemplateComponent,
    loadChildren: () => import('./template/template.module').then(m => m.TemplateModule),
  },
  {
    path: 'admin', component: AdminComponent,canActivate: [canActivateAuth],
    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule),
  },
  { path: 'login', component: LoginComponent },
  { path: 'reset-password/:token', component: ResetPasswordComponent },
  // { path: 'reset-password-request', component: ResetPasswordRequestComponent },
  { path: '**', redirectTo: '/404-not-found' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
