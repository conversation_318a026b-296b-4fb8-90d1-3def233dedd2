import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NewComponent } from '../shared/new/new.component';
import { InventoryComponent } from '../inventory/inventory/inventory.component';
import { HomeComponent } from '../pages/home/<USER>';
import { NotFoundComponent } from '../pages/not-found/not-found.component';
import { SupportComponent } from '../admin/support/support.component';
import { StandardComponent } from '../admin/standard/standard.component';
import { NewModeComponent } from '../admin/standard/mode/new/new.component';
import { ShoetypeComponent } from '../admin/standard/shoetype/shoetype.component';
import { ModeComponent } from '../admin/standard/mode/mode.component';
import { AnalyseComponent } from '../analyse/analyse/analyse.component';

const routes: Routes = [
  { path: 'home', component: HomeComponent },
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  {
    path: 'last-inventory', children: [
      { path: '', component: InventoryComponent },
      { path: 'new', component: NewComponent },
      { path: 'edit/:Last', component: NewComponent }

    ]
  },
  {
    path: 'last-analyse', children: [
      { path: '', component: AnalyseComponent }

    ]
  },
  { path: 'standards', component: StandardComponent },
  {
      path: 'standards', component: StandardComponent
    },
    {
      path: 'standard/:standard_ref/shoetypes', component: ShoetypeComponent
    },
    {
      path: 'standard/:standard_ref/shoetype/:shoetype_ref/modes', component: ModeComponent
    },
    {
      path: 'standard/:standard_ref/shoetype/:shoetype_ref/modes/new', component: NewModeComponent
    },
  { path: 'support', component: SupportComponent },
  { path: '404-not-found', component: NotFoundComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TemplateRoutingModule { }
