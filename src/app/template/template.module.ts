import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TemplateComponent } from './template.component';
import { SharedModule } from '../shared/shared.module';
import { LayoutsModule } from '../layouts/layouts.module';
import { SceneModule } from '../scene/scene.module';
import { TemplateRoutingModule } from './template-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ObjectToArrayPipe } from '../pipes/enum-to-array.pipe';
import { LastInventoryModule } from '../inventory/last-inventory.module';

@NgModule({
  declarations: [
    TemplateComponent
  ],
  imports: [
    SharedModule,
    CommonModule,
    LayoutsModule,
    SceneModule,
    LastInventoryModule,
    TemplateRoutingModule,
    ReactiveFormsModule,
    FormsModule
  ],
  providers: [
    ObjectToArrayPipe
  ],
  exports: [
    TemplateComponent
  ]
})

export class TemplateModule { }
