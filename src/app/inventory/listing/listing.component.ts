import { inject, signal } from '@angular/core';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { FileService } from 'src/app/services/file.service';
import { LastService } from 'src/app/services/last.service';
import { LocalService } from 'src/app/services/local.service';
import { SearchService } from 'src/app/services/search.service';
import { UserService } from 'src/app/services/user.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { environment } from 'src/environments/environment';
import { ObjectToArrayPipe } from 'src/app/pipes/enum-to-array.pipe';
import { ColumnsSortKey, SortDirectionEnum } from 'src/app/enums/columns-sorting';
import { SortService } from 'src/app/services/sort.service';
import { ColumnsTranslationKey } from 'src/app/enums/columns-translation-key';
import { LastInfosTranslationKey } from 'src/app/shared/enums/translations';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'app-inventory-listing',
  templateUrl: './listing.component.html',
  styleUrls: ['./listing.component.scss']
})
export class ListingComponent implements OnInit, OnDestroy {

  more_pages_before:number[] = [];
  more_pages_after:number[] = [];
  isHoveringBefore: boolean = false
  isHoveringAfter: boolean = false
  subscriptions: Subscription[] = [];
  options = [10, 15, 50, 100, 200]
  showOptions: boolean = false
  height: number = 100;
  selected_last: any = null
  interval_id: any = null
  delete_ref: any
  all_refs: any[] = []
  columns : any[]= []
  selected_list: any[] = []
  tableCustom: boolean = false
  lasts_list: any[] = []
  pagination_showing_list: any[] = []
  delete_popup: boolean = false
  pagination: {
    all: number,
    count: number,
    current: number,
    pageSize: number
  } = {
      all: 1,
      count: 1,
      current: 0,
      pageSize: 15
    };
  columnsSortKey = ColumnsSortKey;
  columnsTranslationKey = ColumnsTranslationKey;
  // Signals
  filters = signal<any>([]);
  showSortIcon = signal<boolean>(false);
  hoveredSortIcon = signal<string>('');
  sortBy = signal<string>('ref');
  sortDirection = signal<SortDirectionEnum>(SortDirectionEnum.DESCENDANT);
  // Pipes
  objectToArray = inject(ObjectToArrayPipe);
  // enums
  lastInfosTranslations = LastInfosTranslationKey;

  constructor(
    private LastService: LastService,
    public UserService: UserService,
    private StlService: FileService,
    public SearchService: SearchService,
    private LocalService: LocalService,
    private MessagesService: MessagesService,
    private SpinnerService: SpinnerService,
    private sortService: SortService,
    private translateService:TranslateService
    ) {
  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe();
    }
    clearInterval(this.interval_id);
    this.SearchService.filters = {}
    this.SearchService.isSearch = false
  }
  ngOnInit(): void {
    // const columns = this.UserService.user.parameters.selected_columns;
    const columns:any[]= [];
    // const firstColumnName = columns[0].name;
    // this.sortBy.set(this.columnsSortKey[columns[firstColumnName == 'Preview' ? 1 : 0].name]);
    // this.sortService.setSortConfig(this.sortBy(), this.sortDirection());

    this.subscriptions.push(this.LocalService.select_subject.subscribe((selected_last) => {
      // console.log(this.selected_last)
      this.selected_last = selected_last;
    }))
    this.subscriptions.push(this.UserService.isAuthenticated().subscribe((response:any) => {
      // console.log(response.data)
      this.UserService.user = response.data.user
      this.pagination.pageSize = response.data.user.parameters.pagination_size
    }))
    this.subscriptions.push(this.LocalService.open_custom_table.subscribe((data) => {
      this.tableCustom = data.open;
    }))
    this.subscriptions.push(this.SearchService.last_list_changed.subscribe((listing) => {
      // console.log(listing)
      // this.sortBy.set(listing.sort);
      // this.sortDirection.set(listing.sortDirection);
      // this.sortService.setSortConfig(this.sortBy(), this.sortDirection());

      // if(listing.pagination.current == 0){
      //   this.selected_list = [];
      //   this.all_refs = [...listing.all];
      // }
      this.lasts_list = listing
      // this.pagination = listing.pagination;
      // let index = listing.lasts.findIndex((Last:any)=>Last.status == 'processing')
      // clearInterval(this.interval_id);
      // if(index != -1){
      //   this.interval_id = setInterval(()=>{
      //     this.SearchService.search.next({ filter: this.SearchService.filters, page: this.pagination.current })
      //   },30000)
      // }else{
      //   clearInterval(this.interval_id);
      // }
      // this.pagination_showing_list = [0, 1, this.pagination.current - 1, this.pagination.current, this.pagination.current + 1, this.pagination.count - 1, this.pagination.count]
      this.SpinnerService.hide()
      // if (this.pagination.current == 0 && this.lasts_list.length > 0) {
      //   this.SpinnerService.show(1);
      // console.log(this.lasts_list[0])
        this.LocalService.selectLast(this.lasts_list[0])
      // } else {
      //   this.SpinnerService.hide(1);
      // }

      // this.more_pages_before = [...Array(this.pagination.count).keys()].filter(
      //   (el, index )=> !this.pagination_showing_list.includes(el) && index < this.pagination.current - 1 
      // )
      // this.more_pages_after = [...Array(this.pagination.count).keys()].filter(
      //   (el, index )=> !this.pagination_showing_list.includes(el) && index > this.pagination.current + 1 
      // )
    }))

    this.subscriptions.push(this.SearchService.search.subscribe((data) => {
      this.subscriptions.push((this.LastService.getSearchByPage().subscribe({
        next: (response: any) => {
          this.format_filters()
          this.SearchService.last_list_changed.next(response.data)
        },
        error: (error) => {
          this.MessagesService.set(error?.message, 'error')
          this.SpinnerService.hide();
        },
      })))
    }))
    this.paginate(0);
  }
  onHover(page:number, isHovering:boolean ){
    page < this.pagination.current ? this.isHoveringBefore = isHovering : this.isHoveringAfter = isHovering;  
  }
  selectOption(option: number) {
    this.pagination.pageSize = option;
    this.sendOptions()
  }
  toggleOptions() {
    this.showOptions = !this.showOptions;
  }
  sendOptions() {
  this.SpinnerService.show()
    this.UserService.setPageSize(this.pagination.pageSize).subscribe({
      next: (data: any) => {
        this.UserService.user.items_number = data.pageSize
        this.LastService.getSearchByPage().subscribe({
          next: (response: any) => {
            this.format_filters()
            this.SearchService.last_list_changed.next(response.data)
            this.SpinnerService.hide()
            this.showOptions = false;
          }
        })
      },
      error: (error) => {
        this.MessagesService.set(error.message, 'error')
        this.SpinnerService.hide();
      }
    })
  }

  deleteConfirm(ref: any){
    this.delete_popup = true
    this.delete_ref = ref
  }
  cancel(){
    this.delete_popup = false
  }
  delete() {
    this.LastService.deleteLast(this.delete_ref).subscribe({
      next: (response: any) => {
        this.cancel()
        this.MessagesService.set(response.message, 'green');
        this.lasts_list = this.lasts_list.filter(last => last.ref !== this.delete_ref);
        this.LocalService.select_subject.next(null)
      },
      error: (err) => {
        this.SpinnerService.hide();
      }
    })
    // this.SpinnerService.show(); 
    // this.StlService.getDownloadStl(ref, type).subscribe({
    //   next: (data) => {
    //     this.StlService.downloadFile(data,'test')
    //     this.SpinnerService.hide(); 
    //   },
    //   error: (err) => {
    //     console.log(err)
    //     this.SpinnerService.hide(); 
    //   },
    // })
  }

 
  downloadExcel() {
    this.SpinnerService.show();
    this.subscriptions.push(this.StlService.getDownloadExcel("last").subscribe({
      next: (data) => {
        const name = 'lastengineers_Last_Inventory' + '.xlsx';
        this.StlService.downloadFile(data, name)
        this.MessagesService.set('Excel ' + this.translateService.instant('COMMON.SUCCESS_DOWNLOAD'), 'green')
        this.SpinnerService.hide();
      },
      error: (err: any) => {
        this.SpinnerService.hide();
        console.log(err)
      },
    }))
  }

  toggleCustomTable() {
    this.LocalService.open_custom_table.next({open:!this.tableCustom, isAverage: false})
  }
  select(last: any) {
    this.LocalService.selectLast(last);
  }
  add(last_ref: any) {
    const index = this.selected_list.findIndex((el)=> el == last_ref)    
    if(index == -1){
      this.selected_list.push(last_ref)
    }else{
      this.selected_list.splice(index,1)
    }
  }

  selectAll($event:any){
    if(this.selected_list.length == this.all_refs.length){
      this.selected_list = []
    }else{
      this.selected_list = [...this.all_refs]
    }
  }
  
  paginate(page: number = 0) {
    this.SpinnerService.show();
    this.SearchService.search.next({ filter: this.SearchService.filters, page});
  }

  search() {
    this.SearchService.open_search.next(true);
  }

  get page_count() {
    return [...Array(this.pagination.count).keys()]
  }
  get first_el() {
    return (this.pagination.current * this.pagination.pageSize) + 1
  }
  get last_el() {
    const last = (this.pagination.current + 1) * this.pagination.pageSize
    return last > this.pagination.all ? this.pagination.all : last;
  }
  more(page: number) {
    return ((page == (this.pagination.current + 2)) || (page == (this.pagination.current - 2))) && page != 0 && (page != (this.pagination.count - 1));
  }
  image(imageSrc: string) {
    return environment.api_url + imageSrc
  }
  clear(filter: string) {
    if (filter == 'all') {
      this.SpinnerService.show();
      this.SearchService.filters = {}
      this.SearchService.isSearch = false
      this.SearchService.search.next({ filter: this.SearchService.filters, page: 0 });
    } else {
      this.SpinnerService.show();
      if(filter.includes('measures_group')){
        let measure_name = filter.split('.')[1]
        delete this.SearchService.filters.measures_group[measure_name]
      }else{
        if (
          filter.includes('gender') || 
          filter.includes('category') || 
          filter.includes('lasttype') || 
          filter.includes('archtype') ||
          filter.includes('countries') ||
          filter.includes('scanners') ||
          filter.includes('direction')
        ) {
            const indexToDelete = +filter.split('-')[1] - 1;
            const clearedOptions = this.objectToArray.transform(this.SearchService.filters).map(f =>
                filter.includes(f.label) ?
                    { ...f, value: f.value.filter((_: any, i: number) => i !== indexToDelete) } :
                    { ...f });
            this.SearchService.filters = Object.fromEntries(clearedOptions.map(({ label, value }) => [[label], value]));
        } else {
            Object.keys(this.SearchService.filters).filter((el) => el.includes(filter)).forEach((fl) => {
                delete this.SearchService.filters[fl]
            })
        }
      }
      this.SearchService.search.next({ filter: this.SearchService.filters, page: 0 });
    }
    
  }
  format_filters() {
    this.filters.set(Object.entries(this.SearchService.getFormattedFilters()));
  }

  selectSortByColumn(sort: string) {
    this.sortBy.set(this.columnsSortKey[sort.trim()]);
    this.sortDirection.update(prevDirection => prevDirection == SortDirectionEnum.DESCENDANT ? SortDirectionEnum.ASCENDANT : SortDirectionEnum.DESCENDANT);
    this.sortService.setSortConfig(this.sortBy(), this.sortDirection());
    this.SpinnerService.show();
    this.SearchService.search.next(
      { 
        filter: this.SearchService.filters, 
        page: this.pagination.current
    });
  }

  onShowSortIcon(sortKey: string) {
    this.hoveredSortIcon.set(sortKey);
  }

  onHideSortIcon() {
    this.hoveredSortIcon.set('');
  }
}
