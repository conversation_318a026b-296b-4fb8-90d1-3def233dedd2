import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { ListingComponent } from './listing/listing.component';
import { SharedModule } from '../shared/shared.module';
import { InventoryComponent } from './inventory/inventory.component';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [
    ListingComponent,
    InventoryComponent,
  ],
  imports: [
    ReactiveFormsModule,
    HttpClientModule,
    CommonModule,
    SharedModule,
    RouterModule,
    TranslateModule
  ],
  exports: [
    InventoryComponent,
    ListingComponent,
  ]
})
export class LastInventoryModule { }
