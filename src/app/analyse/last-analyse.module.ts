import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { ListingComponent } from './listing/listing.component';
import { SharedModule } from '../shared/shared.module';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { AnalyseComponent } from './analyse/analyse.component';
import { AnalyseSearchComponent } from './components/analyse-search/analyse-search.component';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { searchReducer } from './store/search.reducer';
import { SearchEffects } from './store/search.effects';

@NgModule({
  declarations: [
    ListingComponent,
    AnalyseComponent,
    AnalyseSearchComponent
  ],
  imports: [
    ReactiveFormsModule,
    HttpClientModule,
    CommonModule,
    SharedModule,
    RouterModule,
    TranslateModule,
    StoreModule.forFeature('search', searchReducer),
    EffectsModule.forFeature([SearchEffects])
  ],
  exports: [
    ListingComponent,
    AnalyseComponent,
    AnalyseSearchComponent
  ]
})
export class LastAnalyseModule { }
