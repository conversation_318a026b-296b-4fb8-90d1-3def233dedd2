<div (dragend)="dropEnd($event)" (dragover)="dropOver($event)" class="body" [style.grid-template-columns]="width != null ? width +'% 1% ' + (99 - +width) +'%' : '64% 1% 35%'">
    <div class="main position-relative">
        <!-- <app-analyse-listing></app-analyse-listing> -->
        <!-- <app-spinner></app-spinner> -->
    </div>
    <div 
    draggable="true"
    class="dragable"></div>
    <div class="sidebar position-relative">
        <app-analyse-search></app-analyse-search>
        <!-- <app-last-view *ngIf="selected_last != null" [selected_last]="selected_last"></app-last-view>
        <div *ngIf="selected_last == null" class="w-100 h-100 d-flex align-items-center justify-content-center">
            <h2>{{ 'Select Last' | translate }}</h2>
        </div>
        <app-spinner-html *ngIf="report_spinner"></app-spinner-html> -->
    </div>
</div>
