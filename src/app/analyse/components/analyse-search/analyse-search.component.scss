@import "../../../../styles/variables";
.analyse-search-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  margin: 0;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.search-header {
  padding: 16px 20px 0;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid #e1e5e9;

  h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
  }
}

.search-input-container {
  padding: 20px;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 9;
  border-bottom: 1px solid #e1e5e9;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 10px 14px;
  padding-right: 40px;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
  }

  &::placeholder {
    color: #6c757d;
  }
}

.clear-button {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f8f9fa;
  }
}

.clear-icon {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

.search-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 20px;
  
  span {
    color: #6c757d;
    font-size: 14px;
  }
}

.search-error {
  margin-bottom: 20px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  color: #721c24;
}

.error-icon {
  width: 20px;
  height: 20px;
}

.search-results {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

.results-header {
  padding: 16px 0 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;

  h4 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .results-count {
    font-size: 0.9rem;
    color: #6c757d;
  }
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

.result-content {
  flex: 1;
}

.result-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
  font-size: 14px;
}

.result-reference {
  color: #6c757d;
  font-size: 12px;
  line-height: 1.3;
  margin-bottom: 0;
}

.result-created-at {
  margin-left: 12px;
  flex-shrink: 0;
}

.created-at-badge {
  display: inline-block;
  padding: 2px 6px;
  background-color: #e9ecef;
  color: #495057;
  font-size: 10px;
  font-weight: 500;
  border-radius: 8px;
  white-space: nowrap;
}

.result-actions {
  margin-left: 16px;
}

.action-icon {
  width: 20px;
  height: 20px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.result-item:hover .action-icon {
  opacity: 1;
}

.no-results {
  text-align: center;
  padding: 40px 20px;
}

.no-results-message {
  h4 {
    margin: 16px 0 8px 0;
    color: #333;
    font-size: 1.2rem;
  }
  
  p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.no-results-icon {
  width: 48px;
  height: 48px;
  opacity: 0.5;
  margin-bottom: 16px;
}

// Pagination styles
.pagination-container {
  padding: 16px 20px;
  background: #fff;
  border-top: 1px solid #e1e5e9;
  position: sticky;
  bottom: 0;
  z-index: 8;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.pagination-button {
  padding: 6px 12px;
  border: 1px solid #e1e5e9;
  background: #fff;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background-color: #f8f9fa;
    border-color: $primary;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.active {
    background-color: $primary;
    color: white;
    border-color: $primary;
  }
}

.pagination-info {
  font-size: 12px;
  color: #6c757d;
  margin: 0 16px;
  white-space: nowrap;
}

// Responsive design
@media (max-width: 768px) {
  .search-header {
    padding: 12px 16px 0;

    h3 {
      font-size: 1.2rem;
      margin-bottom: 12px;
    }
  }

  .search-input-container {
    padding: 0 16px 12px;
  }

  .search-results {
    padding: 0 16px;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .results-count {
      font-size: 0.8rem;
    }
  }

  .result-item {
    padding: 10px 8px;
    flex-wrap: wrap;
  }

  .result-content {
    flex: 1;
    min-width: 0;
  }

  .result-created-at {
    margin-left: 8px;
    order: 3;
  }

  .result-actions {
    order: 2;
    margin-left: 8px;
  }

  .pagination-container {
    padding: 12px 16px;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 4px;
  }

  .pagination-button {
    padding: 4px 8px;
    font-size: 11px;
  }

  .pagination-info {
    width: 100%;
    text-align: center;
    margin: 8px 0 0 0;
    font-size: 11px;
  }
}
