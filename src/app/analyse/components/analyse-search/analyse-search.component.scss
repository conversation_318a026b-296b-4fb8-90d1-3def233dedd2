.analyse-search-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.search-header {
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.search-input-container {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  padding-right: 40px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
  
  &::placeholder {
    color: #6c757d;
  }
}

.clear-button {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f8f9fa;
  }
}

.clear-icon {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

.search-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 20px;
  
  span {
    color: #6c757d;
    font-size: 14px;
  }
}

.search-error {
  margin-bottom: 20px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  color: #721c24;
}

.error-icon {
  width: 20px;
  height: 20px;
}

.search-results {
  margin-bottom: 20px;
}

.results-header {
  margin-bottom: 16px;
  
  h4 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
  }
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
  }
}

.result-content {
  flex: 1;
}

.result-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-size: 16px;
}

.result-description {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.result-category {
  margin-top: 8px;
}

.category-badge {
  display: inline-block;
  padding: 4px 8px;
  background-color: #e9ecef;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.result-actions {
  margin-left: 16px;
}

.action-icon {
  width: 20px;
  height: 20px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.result-item:hover .action-icon {
  opacity: 1;
}

.no-results {
  text-align: center;
  padding: 40px 20px;
}

.no-results-message {
  h4 {
    margin: 16px 0 8px 0;
    color: #333;
    font-size: 1.2rem;
  }
  
  p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
  }
}

.no-results-icon {
  width: 48px;
  height: 48px;
  opacity: 0.5;
  margin-bottom: 16px;
}

// Responsive design
@media (max-width: 768px) {
  .analyse-search-container {
    padding: 16px;
    margin: 0 16px;
  }
  
  .result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .result-actions {
    margin-left: 0;
    align-self: flex-end;
  }
}
