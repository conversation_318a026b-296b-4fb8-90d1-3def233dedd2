import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import * as SearchActions from '../../store/search.actions';
import * as SearchSelectors from '../../store/search.selectors';
import { SearchResult } from '../../store/search.actions';

@Component({
  selector: 'app-analyse-search',
  templateUrl: './analyse-search.component.html',
  styleUrls: ['./analyse-search.component.scss']
})
export class AnalyseSearchComponent implements OnInit, OnDestroy {
  searchControl = new FormControl('');
  
  searchQuery$: Observable<string>;
  searchResults$: Observable<SearchResult[]>;
  loading$: Observable<boolean>;
  error$: Observable<string | null>;
  hasResults$: Observable<boolean>;

  private subscriptions: Subscription[] = [];

  constructor(private store: Store) {
    this.searchQuery$ = this.store.select(SearchSelectors.selectSearchQuery);
    this.searchResults$ = this.store.select(SearchSelectors.selectSearchResults);
    this.loading$ = this.store.select(SearchSelectors.selectSearchLoading);
    this.error$ = this.store.select(SearchSelectors.selectSearchError);
    this.hasResults$ = this.store.select(SearchSelectors.selectHasSearchResults);
  }

  ngOnInit(): void {
    // Load all data by default on initialization
    this.store.dispatch(SearchActions.searchItems({ query: '' }));

    // Subscribe to search control changes
    const searchSubscription = this.searchControl.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(query => {
      if (query && query.trim()) {
        this.store.dispatch(SearchActions.searchItems({ query: query.trim() }));
        console.log('Search query:', query.trim());
      } else {
        // Load all data when search is cleared
        this.store.dispatch(SearchActions.searchItems({ query: '' }));
      }
    });

    this.subscriptions.push(searchSubscription);

    // Sync form control with store
    const querySubscription = this.searchQuery$.subscribe(query => {
      if (this.searchControl.value !== query) {
        this.searchControl.setValue(query, { emitEvent: false });
      }
    });

    this.subscriptions.push(querySubscription);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onClearSearch(): void {
    this.searchControl.setValue('');
    // Load all data when search is cleared
    this.store.dispatch(SearchActions.searchItems({ query: '' }));
  }

  onResultClick(result: SearchResult): void {
    console.log('Result clicked:', result);
    // Handle result click - navigate to detail view, etc.
  }

  trackByResultRef(_index: number, result: SearchResult): string {
    return result.ref;
  }
}
