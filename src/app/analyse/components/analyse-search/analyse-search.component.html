<div class="analyse-search-container">
  <div class="search-header">
    <h3>{{ 'Search Analysis' | translate }}</h3>
  </div>

  <div class="search-input-container">
    <div class="search-input-wrapper">
      <input 
        type="text" 
        [formControl]="searchControl"
        class="search-input"
        [placeholder]="'Enter search term...' | translate"
        autocomplete="off">
      
      <button 
        *ngIf="searchControl.value" 
        type="button" 
        class="clear-button"
        (click)="onClearSearch()"
        [title]="'Clear search' | translate">
        <img src="assets/icons/cross-icon.svg" alt="Clear" class="clear-icon">
      </button>
    </div>
  </div>

  <div class="search-status" *ngIf="loading$ | async">
    <app-spinner></app-spinner>
    <span>{{ 'Searching...' | translate }}</span>
  </div>

  <div class="search-error" *ngIf="error$ | async as error">
    <div class="error-message">
      <img src="assets/icons/error-icon.svg" alt="Error" class="error-icon">
      <span>{{ error | translate }}</span>
    </div>
  </div>

  <div class="search-results" *ngIf="hasResults$ | async">
    <div class="results-header">
      <h4>{{ 'Search Results' | translate }}</h4>
    </div>
    
    <div class="results-list">
      <div 
        *ngFor="let result of searchResults$ | async; trackBy: trackByResultId"
        class="result-item"
        (click)="onResultClick(result)">
        
        <div class="result-content">
          <div class="result-title">{{ result.title }}</div>
          <div class="result-description">{{ result.description }}</div>
          <div class="result-category" *ngIf="result.category">
            <span class="category-badge">{{ result.category | translate }}</span>
          </div>
        </div>
        
        <div class="result-actions">
          <img src="assets/icons/arrow-right-icon.svg" alt="View" class="action-icon">
        </div>
      </div>
    </div>
  </div>

  <div class="no-results" *ngIf="(searchControl.value && !(loading$ | async) && !(hasResults$ | async) && !(error$ | async))">
    <div class="no-results-message">
      <img src="assets/icons/search-empty-icon.svg" alt="No results" class="no-results-icon">
      <h4>{{ 'No results found' | translate }}</h4>
      <p>{{ 'Try adjusting your search terms' | translate }}</p>
    </div>
  </div>
</div>
