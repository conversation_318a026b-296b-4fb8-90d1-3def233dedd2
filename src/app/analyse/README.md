# Analyse Module - Search Component with NgRx

This module contains a simple search component implemented with NgRx state management.

## Features

- **Search Component**: A reactive search component with real-time search functionality
- **NgRx State Management**: Complete state management with actions, reducers, effects, and selectors
- **Debounced Search**: Automatic debouncing to prevent excessive API calls
- **Loading States**: Visual feedback during search operations
- **Error Handling**: Proper error handling and display
- **Responsive Design**: Mobile-friendly responsive layout

## Components

### AnalyseSearchComponent
- **Selector**: `app-analyse-search`
- **Location**: `src/app/analyse/components/analyse-search/`
- **Features**:
  - Real-time search with debouncing (300ms)
  - Clear search functionality
  - Loading spinner during search
  - Error message display
  - Results list with click handling
  - Responsive design

## NgRx Structure

### Actions (`src/app/analyse/store/search.actions.ts`)
- `searchItems`: Trigger search with query
- `searchItemsSuccess`: Handle successful search results
- `searchItemsFailure`: Handle search errors
- `clearSearch`: Clear search results and query
- `setSearchQuery`: Set search query without triggering search

### Reducer (`src/app/analyse/store/search.reducer.ts`)
- Manages search state including query, results, loading, and error states

### Effects (`src/app/analyse/store/search.effects.ts`)
- Handles async search operations with debouncing and error handling

### Selectors (`src/app/analyse/store/search.selectors.ts`)
- `selectSearchQuery`: Get current search query
- `selectSearchResults`: Get search results
- `selectSearchLoading`: Get loading state
- `selectSearchError`: Get error state
- `selectHasSearchResults`: Check if results exist

### Service (`src/app/analyse/services/analyse-search.service.ts`)
- **Backend Integration**: Makes real HTTP requests to `/api/{lang}/analyse/search`
- **Fallback Mechanism**: Automatically falls back to mock data if API fails
- **Error Handling**: Comprehensive error handling with logging
- **Flexible Response Mapping**: Transforms various API response formats
- **Development Mode**: Toggle between real API and mock data

## Usage

The search component is automatically included in the analyse component template:

```html
<app-analyse-search></app-analyse-search>
```

## Installation Requirements

Make sure to install the required NgRx packages:

```bash
npm install @ngrx/store@^16.3.0 @ngrx/effects@^16.3.0 @ngrx/store-devtools@^16.3.0
```

## Configuration

The NgRx store is configured at two levels:

1. **Root Level** (`app.module.ts`):
   - `StoreModule.forRoot({})`
   - `EffectsModule.forRoot([])`
   - `StoreDevtoolsModule.instrument()`

2. **Feature Level** (`last-analyse.module.ts`):
   - `StoreModule.forFeature('search', searchReducer)`
   - `EffectsModule.forFeature([SearchEffects])`

## Backend Integration

The search service is now configured to make real HTTP requests to your backend API.

### API Endpoint

The service calls: `GET /api/{lang}/analyse/search?q={query}&limit=20`

Where:
- `{lang}` is the current language (e.g., 'en', 'fr')
- `{query}` is the search term
- `limit=20` limits results to 20 items

### Expected API Response

The service can handle various response formats:

```typescript
// Option 1: Direct array
SearchResult[]

// Option 2: Wrapped in data property
{
  data: SearchResult[]
}

// Option 3: Wrapped in results property
{
  results: SearchResult[]
}
```

### SearchResult Interface

Your API should return data that can be mapped to:

```typescript
interface SearchResult {
  id: string;        // item.id || item.ref || item.uuid
  title: string;     // item.title || item.name || item.reference
  description: string; // item.description || item.summary || item.details
  category?: string;   // item.category || item.type
}
```

### Development Configuration

Toggle between real API and mock data:

```typescript
// In analyse-search.service.ts
private readonly useMockData = false; // Set to true for development
```

### Error Handling

The service includes automatic fallback to mock data if the API fails, ensuring the component continues to work during development.

### Backend Implementation Example

Here's an example of what your backend endpoint might look like:

```php
// PHP/Laravel example
Route::get('/api/{lang}/analyse/search', function (Request $request, $lang) {
    $query = $request->get('q');
    $limit = $request->get('limit', 20);

    $results = AnalyseItem::where('title', 'LIKE', "%{$query}%")
        ->orWhere('description', 'LIKE', "%{$query}%")
        ->limit($limit)
        ->get()
        ->map(function ($item) {
            return [
                'id' => $item->id,
                'title' => $item->title,
                'description' => $item->description,
                'category' => $item->category
            ];
        });

    return response()->json(['data' => $results]);
});
```

```javascript
// Node.js/Express example
app.get('/api/:lang/analyse/search', async (req, res) => {
    const { q: query, limit = 20 } = req.query;

    try {
        const results = await AnalyseItem.find({
            $or: [
                { title: { $regex: query, $options: 'i' } },
                { description: { $regex: query, $options: 'i' } }
            ]
        }).limit(parseInt(limit));

        const mappedResults = results.map(item => ({
            id: item._id,
            title: item.title,
            description: item.description,
            category: item.category
        }));

        res.json({ data: mappedResults });
    } catch (error) {
        res.status(500).json({ error: 'Search failed' });
    }
});
```

## Advanced Usage

### Search with Filters

The service also supports filtered search:

```typescript
searchWithFilters(query: string, filters: any): Observable<SearchResult[]>
```

This makes a POST request to the same endpoint with additional filter parameters.

## Customization

### Styling
The component uses SCSS with responsive design. Customize the styles in:
`src/app/analyse/components/analyse-search/analyse-search.component.scss`

### Search Result Interface
Modify the `SearchResult` interface in `search.actions.ts` to match your data structure:

```typescript
export interface SearchResult {
  id: string;
  title: string;
  description: string;
  category?: string;
  // Add more fields as needed
}
```

## Development

The component includes:
- TypeScript strict mode compliance
- Proper subscription management
- Memory leak prevention
- Accessibility considerations
- Error boundary handling

## Testing

To test the search functionality:
1. Type in the search input
2. Results will appear after 300ms debounce
3. Click on results to see console output
4. Use the clear button to reset search
5. Check Redux DevTools for state changes
