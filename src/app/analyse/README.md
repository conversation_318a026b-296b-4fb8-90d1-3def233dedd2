# Analyse Module - Search Component with NgRx

This module contains a simple search component implemented with NgRx state management.

## Features

- **Search Component**: A reactive search component with real-time search functionality
- **NgRx State Management**: Complete state management with actions, reducers, effects, and selectors
- **Debounced Search**: Automatic debouncing to prevent excessive API calls
- **Loading States**: Visual feedback during search operations
- **Error Handling**: Proper error handling and display
- **Responsive Design**: Mobile-friendly responsive layout

## Components

### AnalyseSearchComponent
- **Selector**: `app-analyse-search`
- **Location**: `src/app/analyse/components/analyse-search/`
- **Features**:
  - Real-time search with debouncing (300ms)
  - Clear search functionality
  - Loading spinner during search
  - Error message display
  - Results list with click handling
  - Responsive design

## NgRx Structure

### Actions (`src/app/analyse/store/search.actions.ts`)
- `searchItems`: Trigger search with query
- `searchItemsSuccess`: Handle successful search results
- `searchItemsFailure`: Handle search errors
- `clearSearch`: Clear search results and query
- `setSearchQuery`: Set search query without triggering search

### Reducer (`src/app/analyse/store/search.reducer.ts`)
- Manages search state including query, results, loading, and error states

### Effects (`src/app/analyse/store/search.effects.ts`)
- Handles async search operations with debouncing and error handling

### Selectors (`src/app/analyse/store/search.selectors.ts`)
- `selectSearchQuery`: Get current search query
- `selectSearchResults`: Get search results
- `selectSearchLoading`: Get loading state
- `selectSearchError`: Get error state
- `selectHasSearchResults`: Check if results exist

### Service (`src/app/analyse/services/analyse-search.service.ts`)
- Currently returns mock data for demonstration
- Ready to be connected to real API endpoints

## Usage

The search component is automatically included in the analyse component template:

```html
<app-analyse-search></app-analyse-search>
```

## Installation Requirements

Make sure to install the required NgRx packages:

```bash
npm install @ngrx/store@^16.3.0 @ngrx/effects@^16.3.0 @ngrx/store-devtools@^16.3.0
```

## Configuration

The NgRx store is configured at two levels:

1. **Root Level** (`app.module.ts`):
   - `StoreModule.forRoot({})`
   - `EffectsModule.forRoot([])`
   - `StoreDevtoolsModule.instrument()`

2. **Feature Level** (`last-analyse.module.ts`):
   - `StoreModule.forFeature('search', searchReducer)`
   - `EffectsModule.forFeature([SearchEffects])`

## Customization

### Connecting to Real API
Update the `AnalyseSearchService.searchItems()` method to call your actual API:

```typescript
searchItems(query: string): Observable<SearchResult[]> {
  return this.http.get<SearchResult[]>(`${environment.api_url}api/analyse/search?q=${encodeURIComponent(query)}`);
}
```

### Styling
The component uses SCSS with responsive design. Customize the styles in:
`src/app/analyse/components/analyse-search/analyse-search.component.scss`

### Search Result Interface
Modify the `SearchResult` interface in `search.actions.ts` to match your data structure:

```typescript
export interface SearchResult {
  id: string;
  title: string;
  description: string;
  category?: string;
  // Add more fields as needed
}
```

## Development

The component includes:
- TypeScript strict mode compliance
- Proper subscription management
- Memory leak prevention
- Accessibility considerations
- Error boundary handling

## Testing

To test the search functionality:
1. Type in the search input
2. Results will appear after 300ms debounce
3. Click on results to see console output
4. Use the clear button to reset search
5. Check Redux DevTools for state changes
