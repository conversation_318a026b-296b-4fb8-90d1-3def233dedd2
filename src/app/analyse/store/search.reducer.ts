import { createReducer, on } from '@ngrx/store';
import * as SearchActions from './search.actions';
import { SearchResult } from './search.actions';

export interface SearchState {
  query: string;
  results: SearchResult[];
  loading: boolean;
  error: string | null;
}

export const initialState: SearchState = {
  query: '',
  results: [],
  loading: false,
  error: null
};

export const searchReducer = createReducer(
  initialState,
  on(SearchActions.setSearchQuery, (state, { query }) => ({
    ...state,
    query
  })),
  on(SearchActions.searchItems, (state, { query }) => ({
    ...state,
    query,
    loading: true,
    error: null
  })),
  on(SearchActions.searchItemsSuccess, (state, { results }) => ({
    ...state,
    results,
    loading: false,
    error: null
  })),
  on(SearchActions.searchItemsFailure, (state, { error }) => ({
    ...state,
    results: [],
    loading: false,
    error
  })),
  on(SearchActions.clearSearch, (state) => ({
    ...state,
    query: '',
    results: [],
    loading: false,
    error: null
  }))
);
