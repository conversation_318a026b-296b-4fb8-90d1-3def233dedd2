import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, catchError, switchMap, debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';
import * as SearchActions from './search.actions';
import { AnalyseSearchService } from '../services/analyse-search.service';

@Injectable()
export class SearchEffects {

  searchItems$ = createEffect(() =>
    this.actions$.pipe(
      ofType(SearchActions.searchItems),
      filter(action => action.query.trim().length > 0),
      debounceTime(300),
      distinctUntilChanged((prev, curr) => prev.query === curr.query),
      switchMap(action =>
        this.searchService.searchItems(action.query).pipe(
          map(results => SearchActions.searchItemsSuccess({ results })),
          catchError(error => of(SearchActions.searchItemsFailure({ 
            error: error.message || 'Search failed' 
          })))
        )
      )
    )
  );

  constructor(
    private actions$: Actions,
    private searchService: AnalyseSearchService
  ) {}
}
