import { createAction, props } from '@ngrx/store';

export interface SearchResult {
  ref: string;
  reference: string;
  created_at: string;
}

// Search Actions
export const searchItems = createAction(
  '[Search] Search Items',
  props<{ query: string }>()
);

export const searchItemsSuccess = createAction(
  '[Search] Search Items Success',
  props<{ results: SearchResult[] }>()
);

export const searchItemsFailure = createAction(
  '[Search] Search Items Failure',
  props<{ error: string }>()
);

export const clearSearch = createAction(
  '[Search] Clear Search'
);

export const setSearchQuery = createAction(
  '[Search] Set Search Query',
  props<{ query: string }>()
);
