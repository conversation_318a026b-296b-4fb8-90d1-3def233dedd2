<div class="h-100 w-100 d-flex flex-column" (click)="showOptions = false; $event.stopPropagation()">
    <div class="d-flex flex-column">
        <div class="d-flex align-items-center justify-content-between w-100">
            <div class="d-flex align-items-center justify-content-start">
                <h3 class="title">{{ 'Inventory List' | translate }}</h3>
                <button class="btn btn-primary ps-2 pe-3 recherche-btn pointer" (click)="search()">
                    <img class="me-2" src="assets/icons/recherche-icon.svg" alt="search icon" widdiv="20px"
                        height="20px">
                    <span class="m-0 p-0">{{ 'Search' | translate }}</span>
                </button>
            </div>
            <div class="d-flex align-items-center justify-content-end table-parameter" >
                <div class="show_elements" (click)="toggleOptions(); $event.stopPropagation();">
                    <p class="m-0">{{ 'Number of items to show' | translate }} : </p> <span>{{ pagination.pageSize }}</span> 
                    <img src="assets/icons/open-section-icon.svg" alt="Open section icon" >
                </div>
                    <ul class="list-options" *ngIf="showOptions">
                    <li *ngFor="let option of options" (click)="selectOption(option)"
                        [ngClass]="pagination.pageSize == option ? 'bg-secondary': '' ">
                        {{ option }}
                    </li>
                </ul>
                <div class="excel" (click)="downloadExcel()">
                    <p>{{ 'Export Data in EXCEL' | translate }}</p> <img src="assets/icons/download-excel-icon.svg" alt="Download Excel">
                </div>
                <button class="btn-icon">
                    <img src="assets/icons/parameter-icon.svg" alt="Table Parameter"  style="cursor: pointer;" (click)="toggleCustomTable(); $event.stopPropagation()">
                </button>
            </div>
        </div>
        <div class="w-100 d-flex align-items-center justify-content-start">
        </div>
    </div>
    <div class="d-flex justify-content-center align-items-center w-100 overflow-auto" *ngIf="SearchService.isSearch">
        <!-- <p class="m-0 px-2">{{ 'FILTERS.HEADER' | translate }}:</p>
        <div class="d-flex w-100 overflow-auto p-1">
            <span class="filter" *ngFor="let filter of filters()">
                <p>{{ filter[1] | titlecase}}</p>
                <img (click)="clear(filter[0])" src="assets/icons/white-cross-icon.svg" alt="Close Search">
            </span>
            <span class="filter all" (click)="clear('all')">
                <p>{{ 'COMMON.ALL' | translate }}</p>
                <img src="assets/icons/white-cross-icon.svg" alt="Close Search">
            </span>
        </div> -->

    </div>
    <div class="flex-grow-1" style="height: 0;">
        <div class="w-100 h-100 overflow-y-auto table-container">
            <!-- {{height}} -->
            <table class="table listing-table">
                <thead class="table-dark sticky">
                    <tr>
                        <th scope="col">
                            <div>
                                <div class="dm-checkbox-input-select">
                                    <input type="checkbox" name="select_all" id="select_all" 
                                    [checked]="all_refs.length > 0 && selected_list.length == all_refs.length"
                                    (change)="selectAll($event)">
                                    <label
                                    (click)="$event.stopPropagation();"
                                    [attr.semi-checked]="all_refs.length > 0 && selected_list.length > 0 && selected_list.length != all_refs.length" for="select_all"></label>
                                </div>
                            </div>
                        </th>
                        <th 
                            scope="col" 
                            *ngFor="let column of UserService.user.parameters.selected_columns;" 
                            (click)="selectSortByColumn(column.name)"
                            (mouseenter)="onShowSortIcon(column.name)"
                            (mouseleave)="onHideSortIcon()"
                            style="cursor: pointer;"
                            [ngStyle]="{'pointer-events': column.name === 'Preview' ? 'none' : 'auto'}"
                        >
                            <img 
                                src="assets/icons/sort-btn.svg" 
                                alt="sort button"
                                [style.opacity]="sortBy() == columnsSortKey[column.name.trim()] ? 1 : (hoveredSortIcon() == column.name) ? 0.7 : 0"
                            >
                            {{ column.name | translate }}
                        </th>
                        <th scope="col">{{ 'Actions' | translate }}</th>
                        <th scope="col"></th>
                    </tr>
                </thead>
              
                <tbody *ngIf="lasts_list.length > 0">
                    <tr *ngFor="let last of lasts_list" (click)="select(last)">
                        <!-- [ngClass]="{active: (selected_last && last.ref == selected_last.ref)}"> -->
                        <td scope="row">
                            <div>
                                <div class="dm-checkbox-input-select" style="width: fit-content;" (click)="add(last.ref);$event.stopPropagation()">
                                    <input type="checkbox" name="{{ last.ref }}" id="{{ last.ref }}"
                                        [checked]="selected_list.includes(last.ref)">
                                    <label for="{{ last.ref }}" class="disabled"></label>
                                </div>
                            </div>
                        </td>
                        <!-- <td scope="row">
                            <div>
                                <div class="dm-checkbox-input-select">
                                    <input type="checkbox" name="{{ last.ref }}" id="{{ last.ref }}"
                                        [checked]="last.ref == (selected_last && selected_last.ref)">
                                    <label class="disabled" for="{{ last.ref }}"></label>
                                </div>
                            </div>
                        </td> -->
                        <td *ngFor="let column of UserService.user.parameters.selected_columns;">
                            <div *ngIf="column.slug">
                                <ng-container *ngIf="column.slug === 'image'; else textContent">
                                    <img [src]="image(last[column.slug])" alt="last image" width="60px" style="max-height: 30px;">
                                </ng-container>
                                <ng-template #textContent>
                                  <span>
                                    {{ last[column.slug] ?
                                    (last[column.slug] | translate) :
                                    (last[column.slug] | empty | translate) }} 
                                </span>
                                </ng-template>
                            </div>
                        </td>                        
                        <td>
                            <button class="btn btn-delete pointer" (click)="deleteConfirm(last.ref);$event.stopPropagation();">
                                <img src="assets/icons/delete-icon.svg" alt="delete icon" width="100%">
                            </button>
                        </td>
                        <td>
                        </td>
                    </tr>
                </tbody>
            </table>
            <!-- <h1 class="w-100 text-center pt-3" *ngIf="!(lasts_list.length > 0)">{{ 'DATABASE.EMPTY_LIST_MESSAGE' | translate }}</h1> -->
            <h1 class="w-100 text-center pt-3" *ngIf="!(lasts_list.length > 0)">{{ 'List Empty' | translate }}</h1>
        </div>
    </div>
    <div class="d-flex align-items-center justify-content-between">
        <button class="btn btn-primary px-3 rounded-40" [routerLink]="['/last-inventory/new']">
            <img class="p-1 pe-2" src="assets/icons/plus-icon.svg" alt="plus icon">
            {{'Add New' | translate }}
        </button>
        <div class="d-flex align-items-center justify-content-end">
            <div class="pagination">
                <img src="assets/icons/pagination-previous.svg" alt="pagination previous icon"
                    [ngClass]="{disabled: pagination.current == 0}" (click)="paginate(pagination.current - 1)">
                <ng-container *ngFor="let page of page_count">
                    <button *ngIf="pagination_showing_list.includes(page) || more(page)" (click)="paginate(page)"
                        [disabled]="more(page)" [ngClass]="{active: page == pagination.current, more: more(page) }">
                        <b *ngIf="more(page)" (mouseenter)="onHover(page,true)" (mouseleave)="onHover(page,false)">
                            <div *ngIf="page < pagination.current && more_pages_before.length > 0 && isHoveringBefore">
                                <span *ngFor="let more_page_item of more_pages_before" (click)="paginate(more_page_item)">
                                    {{ more_page_item + 1 }}
                                </span>
                            </div>
                            <div *ngIf="page > pagination.current && more_pages_after.length > 0 && isHoveringAfter">
                                <span *ngFor="let more_page_item of more_pages_after" (click)="paginate(more_page_item)">
                                    {{ more_page_item + 1 }}
                                </span>
                            </div>                            
                            ...</b>
                        <b *ngIf="!more(page)">{{ page + 1 }}</b>
                    </button>
                </ng-container>
                <img src="assets/icons/pagination-next.svg" alt="pagination next icon"
                    [ngClass]="{disabled: pagination.current == pagination.count - 1}"
                    (click)="paginate(pagination.current +1)">
            </div>
            <p class="shown" innerHtml="{{ 'Pagination' | translate:{first_el, last_el, 'all_el': pagination.all} }}"></p>
            <!-- <p class="shown">Pagination</p> -->
        </div>
    </div>
</div>

<app-popup-ui  (cancel)="cancel()" *ngIf="delete_popup">
    <h4 title class="text-center text-primary">
        {{ 'Delete' | translate }}
    </h4>
    <p message>
        {{ ('Delete this last from ' | translate) + '?' }}
    </p>
    <ng-container actions>
        <button class="btn btn-danger" (click)="cancel()">{{ 'Cancel' | translate }}</button>
        <button class="btn btn-primary" (click)="delete()">{{ 'Delete' | translate }}</button>
    </ng-container>
</app-popup-ui>
