@import "../../../styles/variables";

.title {
    font-weight: 900;
    margin-bottom: 0;
    margin-right: 2rem;
    font-size: 1.4rem;
}

.recherche-btn {
    padding-right: 1rem !important;
    border: 0;

    &>img {
        margin-right: 0.5rem !important;
    }

    &>span {
        font-size: 0.8rem;
        line-height: 1.8rem;
    }
}

.pagination {
        display: flex;
        align-items: center;
        justify-content: center;
    &>* {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4rem;
        background-color: $secondary;
        padding: 0.3rem 0 0.1rem 0;
        margin: 0.5rem;
        width: 2rem;
        height: 2rem;
        color: rgba($dark , 0.75);
        font-weight: 700;
        line-height: 10px;
        cursor: default;
        border: 0;
        outline: none;       
        &:focus{
            border: 0;
            outline: none;
        }
        
        &.disabled{
            pointer-events: none;
            cursor: not-allowed;
        }
        &.active{
            background-color: $primary;
            color: white;
        }

        &:hover{
            background-color: rgba($primary, 0.5);
            transform: scale(1.1);
        }
        &.more{
            position: relative;
            &>b{
                cursor: default;
                padding-bottom: 0.5rem;
            }
            div{
                outline-width: 0 !important;
                position: absolute;
                display: flex;
                flex-direction: column;
                bottom: -20%;
                left: -30%;
                padding: 3px;
                margin: 3px;
                border-radius: 0.3rem;
                background-color: #DDC898;
                overflow-y: auto;
                max-height: 10rem;
                width: 40px;
                span{
                    display: block !important;
                    padding: 8px 3px;
                    color: $dark;
                    margin: 2px;
                    border-radius: 5px;
                    &:hover{
                        background-color: rgba($dark , 0.3);
                    }
                }
            }
            &:hover {
                transform: unset !important;
            }
        }
    }

    &>img {
        padding: 0.5rem;
    }
}

.shown {
    font-weight: 700;
    color: rgba($dark , 0.75);
    margin: 0 1rem;
    font-size: 1rem;
}

.sticky {
    position: sticky;
    top: 0;
    bottom: 0;
    z-index: 10;
}

.table-container {
    border-radius: 0.5rem;
    overflow: hidden;

    .listing-table {
        border-collapse: separate;
        border-spacing: 0;
        &> :not(caption)>*>* {
            padding: 0.5rem 0.5rem;
            background-color: unset;
            border-bottom-width: 0;
        }

        thead tr {
            &>th {
                font-size: 0.8rem;
                font-weight: 500;
                background-color: $dark;
                position: relative;
                text-align: center;
                &:nth-last-child(1) {
                    padding: 0;
                    width: 5px;
                }
            }

            img {
                position: relative;
                top: -1px;
                cursor: pointer;
            }
        }

        tbody>tr {            
            &:nth-child(2n+1) {
                background-color: rgba($dark , 0.03);
            }

            &:hover {
                background-color: rgba($dark , 0.05);
            }
            &.active{
                td {
                    border: solid 1px $primary;
                    border-style: solid none;
                    border-bottom-width: 1px;
                }
                    
                td:nth-child(1) {
                    border-left-style: solid;
                    border-top-left-radius: 1rem; 
                    border-bottom-left-radius: 1rem;
                }
                td:nth-last-child(2) {
                    border-right-style: solid;
                    border-bottom-right-radius: 1rem; 
                    border-top-right-radius: 1rem; 
                }
                
            } 

            td {
                position: relative;
                vertical-align: middle;
                text-align: center;
                &:nth-last-child(1) {
                    border: 0;
                    background-color: white;
                    pointer-events: none;
                    padding: 0;
                    width: 0;
                }

                * {
                    font-size: 0.9rem;
                    font-weight: 500;
                    &.ref{
                        text-transform: uppercase;
                        font-size: 0.7rem;
                        padding-top: 0.3rem;
                      }
                }

                span {
                    display: block;
                    background-color: rgba($dark , 0.75);
                    color: white;
                    padding: 0.15rem 0.5rem 0 0.5rem;
                    border-radius: 1rem;
                    width: fit-content;
                    min-width: 4rem;
                    text-align: center;
                    margin: auto;
                }

                &:nth-child(2n) {
                    span {
                        background-color: darken($secondary , 0.5);
                        color: $dark;

                    }
                }

                &:nth-child(2n+1) {
                    span {
                        background-color: rgba($dark , 0.65);
                        color: white;
                    }
                }

                &:nth-child(3) {
                    span {
                        background-color: lighten($dark , 0.25);
                        color: white;

                    }
                }

                .btn-delete {
                    width: 30px;
                    height: 30px;
                    padding: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 2rem;
                    background-color: rgba($dark , 0.15);
                    border: 0;
                    transition: transform 300ms ease-in;
                    margin: auto;

                    &:hover {
                        border: 0;
                        transform: scale(1.1);
                    }
                }
            }
        }
    }
}
.table-parameter{
    position: relative;
    &>.btn-icon{
        padding: 0.1rem;
        width: 1.5rem;
        height: 1.5rem;
        margin: 0 0.5rem;
        &>img{
            width: 60%;
        }
    }
    &>.excel{
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba($primary, 0.2);
        color: $primary;
        border-radius: 1rem;
        padding: 0.05rem 1rem;
        margin: 0 0.5rem;
        border: 1px solid $white;
        cursor: pointer;
        &:hover {
            border: 1px solid $primary;
        }
        &>p{
            font-size: 0.9rem;
            margin: 0.2rem 0 0 0;
            cursor: pointer;
        }
        &>img{
            margin-left: 0.5rem;
            width: 0.9rem;
        }
    }
    &>.show_elements{
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
        background-color: rgba($secondary, 0.5);
        color: $dark;
        border-radius: 1rem;
        padding: 0.05rem 1rem;
        margin: 0 0.5rem;
        &>p{
            font-size: 0.9rem;
            margin: 0.2rem 0 0 0;
        }
        &>img{
            margin-left: 0.5rem;
            width: 0.9rem;
        }
        &>span{
            margin-left: 0.5rem;
            font-size: 0.9rem;
            background-color: $primary;
            color: white;;
            display: block;
            line-height: 1;
            border-radius: 1rem;
            padding: 0.25em 0.4em;
        }
    }
    &>.list-options {
        width: 5rem;
        height: 14rem;
        list-style-type: none;
        padding: 0;
        margin: 0;
        right: 16rem;
        top: 0.1rem;
        border-radius: 0.3rem;
        z-index: 11;
        background-color: white;
        position: absolute;
        box-shadow: -0.2rem -0.2rem 0.5rem rgba(0, 0, 0, 0.1), 0.2rem 0.2rem 0.5rem rgba(0, 0, 0, 0.1);
       
    } &>.list-options>li {
        width: 100%;
        font-size: 1.2rem;
        padding: 0.5rem 0 0.5rem 1rem;

    } &>.list-options>li:hover {
        background-color: rgba($secondary, 0.5);
    }
}
