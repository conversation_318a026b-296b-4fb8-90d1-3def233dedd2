import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { SearchResult } from '../store/search.actions';
import { environment } from '../../../environments/environment';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class AnalyseSearchService {

  constructor(
    private http: HttpClient,
    private translateService: TranslateService
  ) { }

  searchItems(query: string): Observable<SearchResult[]> {
    // Set up HTTP parameters
    const params = new HttpParams()
      .set('reference', query)
      .set('limit', '20'); // Limit results to 20 items

    // Make API call to backend
    return this.http.get<any>(`${environment.api_url}api/${this.lang}/analyse/lasts`, { params })
      .pipe(
        map((response: any) => this.transformApiResponse(response)),
        catchError((error) => {
          console.error('Search API error:', error);
          // Re-throw the error to be handled by NgRx effects
          throw error;
        })
      );
  }

  /**
   * Transform the API response to match our SearchResult interface
   * Modify this method based on your actual API response structure
   */
  private transformApiResponse(response: any): SearchResult[] {
    // Handle different possible response structures
    const data = response.data || response.results || response;

    if (!Array.isArray(data)) {
      return [];
    }

    return data.map((item: any) => ({
      ref: item.ref ,
      reference: item.reference || 'N/A',
      created_at: item.created_at || 'N/A'
    }));
  }

  /**
   * Alternative method for searching with filters (if needed)
   */
  searchWithFilters(query: string, filters: any = {}): Observable<SearchResult[]> {
    let params = new HttpParams()
      .set('q', query)
      .set('limit', '20');

    // Add filters to params
    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        params = params.set(key, filters[key]);
      }
    });

    return this.http.post<any>(`${environment.api_url}api/${this.lang}/analyse/lasts`,
      { query, filters },
      { params }
    ).pipe(
      map((response: any) => this.transformApiResponse(response)),
      catchError((error) => {
        console.error('Search with filters API error:', error);
        throw error;
      })
    );
  }

  /**
   * Get language for API calls (following the pattern from other services)
   */
  private get lang(): string {
    return this.translateService.getDefaultLang() || 'en';
  }
}
