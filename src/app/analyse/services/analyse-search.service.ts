import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, delay } from 'rxjs';
import { SearchResult } from '../store/search.actions';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AnalyseSearchService {

  constructor(private http: HttpClient) { }

  searchItems(query: string): Observable<SearchResult[]> {
    // For demo purposes, return mock data
    // Replace this with actual API call when backend is ready
    const mockResults: SearchResult[] = [
      {
        id: '1',
        title: `Analysis Result for "${query}"`,
        description: 'This is a sample analysis result matching your search query.',
        category: 'Analysis'
      },
      {
        id: '2',
        title: `Data Point: ${query}`,
        description: 'Relevant data point found in the analysis database.',
        category: 'Data'
      },
      {
        id: '3',
        title: `Report: ${query} Analysis`,
        description: 'Comprehensive report containing analysis for your search term.',
        category: 'Report'
      }
    ].filter(result => 
      result.title.toLowerCase().includes(query.toLowerCase()) ||
      result.description.toLowerCase().includes(query.toLowerCase())
    );

    // Simulate API delay
    return of(mockResults).pipe(delay(500));

    // Uncomment and modify this when you have a real API endpoint:
    // return this.http.get<SearchResult[]>(`${environment.api_url}api/analyse/search?q=${encodeURIComponent(query)}`);
  }
}
