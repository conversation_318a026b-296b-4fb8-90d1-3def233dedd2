import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError, delay } from 'rxjs/operators';
import { SearchResult } from '../store/search.actions';
import { environment } from '../../../environments/environment';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class AnalyseSearchService {
  // Set this to true to use mock data during development
  private readonly useMockData = false;

  constructor(
    private http: HttpClient,
    private translateService: TranslateService
  ) { }

  searchItems(query: string): Observable<SearchResult[]> {
    // Use mock data during development or if API is not available
    if (this.useMockData) {
      return this.getMockResults(query);
    }

    // Set up HTTP parameters
    const params = new HttpParams()
      .set('q', query)
      .set('limit', '20'); // Limit results to 20 items

    // Make API call to backend
    return this.http.get<any>(`${environment.api_url}api/${this.lang}/analyse/search`, { params })
      .pipe(
        map((response: any) => this.transformApiResponse(response)),
        catchError((error) => {
          console.error('Search API error:', error);
          console.warn('Falling back to mock data due to API error');
          // Fallback to mock data if API fails
          return this.getMockResults(query);
        })
      );
  }

  /**
   * Transform the API response to match our SearchResult interface
   * Modify this method based on your actual API response structure
   */
  private transformApiResponse(response: any): SearchResult[] {
    // Handle different possible response structures
    const data = response.data || response.results || response;

    if (!Array.isArray(data)) {
      return [];
    }

    return data.map((item: any) => ({
      id: item.id || item.ref || item.uuid || String(Math.random()),
      title: item.title || item.name || item.reference || 'Untitled',
      description: item.description || item.summary || item.details || 'No description available',
      category: item.category || item.type || 'General'
    }));
  }

  /**
   * Alternative method for searching with filters (if needed)
   */
  searchWithFilters(query: string, filters: any = {}): Observable<SearchResult[]> {
    let params = new HttpParams()
      .set('q', query)
      .set('limit', '20');

    // Add filters to params
    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
        params = params.set(key, filters[key]);
      }
    });

    return this.http.post<any>(`${environment.api_url}api/${this.lang}/analyse/search`,
      { query, filters },
      { params }
    ).pipe(
      map((response: any) => this.transformApiResponse(response)),
      catchError((error) => {
        console.error('Search with filters API error:', error);
        throw error;
      })
    );
  }

  /**
   * Get mock results for development/fallback
   */
  private getMockResults(query: string): Observable<SearchResult[]> {
    const mockResults: SearchResult[] = [
      {
        id: '1',
        title: `Analysis Result for "${query}"`,
        description: 'This is a sample analysis result matching your search query.',
        category: 'Analysis'
      },
      {
        id: '2',
        title: `Data Point: ${query}`,
        description: 'Relevant data point found in the analysis database.',
        category: 'Data'
      },
      {
        id: '3',
        title: `Report: ${query} Analysis`,
        description: 'Comprehensive report containing analysis for your search term.',
        category: 'Report'
      },
      {
        id: '4',
        title: `Measurement: ${query}`,
        description: 'Measurement data related to your search query.',
        category: 'Measurement'
      },
      {
        id: '5',
        title: `Configuration: ${query}`,
        description: 'Configuration settings matching your search criteria.',
        category: 'Configuration'
      }
    ].filter(result =>
      result.title.toLowerCase().includes(query.toLowerCase()) ||
      result.description.toLowerCase().includes(query.toLowerCase())
    );

    // Simulate API delay
    return of(mockResults).pipe(delay(300));
  }

  /**
   * Get language for API calls (following the pattern from other services)
   */
  private get lang(): string {
    return this.translateService.getDefaultLang() || 'en';
  }
}
