export const ColumnsTranslationKey: Record<string, string> = {
    'Preview': 'BACKEND.Preview',
    'Last ID': 'BACKEND.Last ID',
    'Reference': 'BACKEND.Reference',
    'Gender': 'BACKEND.Gender',
    'Category': 'BACKEND.Category',
    'Country': 'BACKEND.Country',
    'L / R': 'BACKEND.L / R',
    'Size': 'BACKEND.Size',
    'Weight': 'BACKEND.Weight',
    'Height': 'BACKEND.Height',
    'Age': 'BACKEND.Age',
    'Last Type': 'BACKEND.Last Type',
    'Arch Type': 'BACKEND.Arch Type',
    'Scanner': 'BACKEND.Scanner',
    'Added Date': 'BACKEND.Added Date',
    'System Size': 'TABLE_HEADERS.SYSTEM_SIZE',
    'Measures Group': 'TABLE_HEADERS.MEASURES_GROUP',

    'Max Height': 'FILTERS.MAX_HEIGHT',
    'Min Height': 'FILTERS.MIN_HEIGHT',
    'Max Weight': 'FILTERS.MAX_WEIGHT',
    'Min Weight': 'FILTERS.MIN_WEIGHT',
    'Max Size': 'FILTERS.MAX_SIZE',
    'Min Size': 'FILTERS.MIN_SIZE',
    'Max Age': 'FILTERS.MAX_AGE',
    'Min Age': 'FILTERS.MIN_AGE',
};