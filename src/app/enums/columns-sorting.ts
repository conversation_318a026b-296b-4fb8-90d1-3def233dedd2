export const ColumnsSortKey: Record<string, string> = {
    'Last ID': 'ref',
    'Reference': 'reference',
    'Gender': 'gender',
    'Category': 'category',
    'Country': 'country',
    'L / R': 'direction',
    'Size': 'size',
    'Weight': 'weight',
    'Height': 'height',
    'Age': 'age',
    'Last Type': 'LastType',
    'Arch Type': 'ArchType',
    'Scanner': 'scanner',
    'Added Date': 'createdAt'
};

export enum SortDirectionEnum {
    ASCENDANT = 'ASC',
    DESCENDANT = 'DESC'
};
