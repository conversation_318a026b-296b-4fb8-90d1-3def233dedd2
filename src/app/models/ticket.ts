export interface Ticket {
    id?: number,
    ref: string;
    subject: string;
    user_name: string;
    created_at: string;
    closed_at?: string;
    messages: Message[];
};

export interface Message {
    id?: number;
    content: string;
    sender: string;
    attachments_count: number;
};

export const ticketInitialization: Ticket = {
    ref: '',
    subject: '',
    user_name: '',
    created_at: '',
    messages: []
};

export const messageInitialization: Message = {
    content: '',
    sender: '',
    attachments_count: 0
};
