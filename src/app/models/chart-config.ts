import { ChartType } from "angular-google-charts"

export interface ChartConfig {
    id?: number;
    title?: string;
    type: ChartType;
    data?: any[][];
    legend?: any[];
    columns?: any[];
    options: {
        chartArea?: {
            width?: string;
            height?: string;
            top?: number
        };
        hAxis?: {
            title?: string
            baselineColor?: string;
            titleTextStyle?: {
                italic?: boolean;
            };
            gridlines?: {
                count?: number;
                minSpacing?: number
            };
            viewWindow?: {
                min?: number
            }
        };
        vAxis?: {
            title?: string
            baselineColor?: string;
            titleTextStyle?: {
                italic?: boolean;
            };
            gridlines?: {
                count?: number;
                minSpacing?: number
            };
            viewWindow?: {
                min?: number
            }
        };
        animation?: {
            duration?: number;
            easing?: string;
            startup?: boolean
        };
        width?: number;
        height?: number;
        colors?: any;
        series?: any;
        lineWidth?: number;
        pieSliceBorderColor?: string;
        pieHole?: number
    };
}
