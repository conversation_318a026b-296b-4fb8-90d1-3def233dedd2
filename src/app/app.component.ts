import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { SpinnerService } from './shared/spinner/spinner.service';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  isLoading: boolean = false;
  title = 'lastengineers';
  subscriptions: Subscription[] = []

  constructor(private SpinnerService: SpinnerService) {}

  ngOnInit(): void {
    this.subscriptions.push(this.SpinnerService.globalSpinnerSubject.subscribe((data) => {
      Promise.resolve().then(() => {
        this.isLoading = data;
      })
    }))
  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }
}
