@import "../../../styles/variables";

.full-body {
    position: relative;
    border-radius: 0.7rem;
    width: 95vw;
    height: 95vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: 0.7rem;

    .title {
        position: absolute;
        transform: translate(-50%, 0%);
        top: 0%;
        left: 50%;
        display: flex;
        flex-direction: column;

        img {
            margin: 1rem;
            width: 14rem;
        }

        h1 {
            font-family: 'Muller';
            font-size: 3rem;
            color: rgba($dark, $alpha: 0.9);
        }
    }

    .reset-password-card {
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        width: 50vw;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
}