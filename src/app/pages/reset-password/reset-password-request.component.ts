import { Component } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { PasswordService } from './password.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-reset-password-request',
  templateUrl: './reset-password-request.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordRequestComponent {
  resetForm: FormGroup;
  isLoading = false;
  
  constructor(
    private router: Router,
    private PasswordService: PasswordService,
    private MessagesService: MessagesService
  ) {
    this.resetForm = new FormGroup({
      email: new FormControl('', {
              validators: [Validators.required, Validators.email],
              nonNullable: true
            }),
    });
  }
  
  onSubmit(): void {
    if (this.resetForm.invalid) {
      return;
    }
    this.isLoading = true
    
    const email = this.resetForm.get('email')?.value;
    
    this.PasswordService.requestPasswordReset({email : email}).subscribe({
      next: (response) => {
        this.MessagesService.set(response.message, 'green')
        this.isLoading = false
        setTimeout(() => {
            this.router.navigate(['/login']);
        }, 3000);
    },
    error: (error) => {
        this.isLoading = false
        this.MessagesService.set(error || 'An error occurred. Please try again later.', 'error')
      }
    });
  }
}