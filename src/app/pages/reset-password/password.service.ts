import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class PasswordService {
  private apiUrl = environment.api_url;
  
  constructor(private http: HttpClient, private TranslateService: TranslateService) { }

  // Your existing auth methods (login, register, etc.)
  
  /**
   * Request a password reset
   * @param email The user's email
  */
  requestPasswordReset(data: {email : string}): Observable<any> {
    return this.http.post(`${this.apiUrl}password/reset-request`, data);
  }

  /**
   * Reset password with token
   * @param token The reset token from the email
   * @param password The new password
   */
  resetPassword(token: string, password: string): Observable<any> {
    return this.http.post(`${this.apiUrl}password/reset`, { token, password });
  }
  get lang(){
    return this.TranslateService.getDefaultLang() ?? localStorage.getItem('lang');
  }
}