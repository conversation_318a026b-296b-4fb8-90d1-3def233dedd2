<div class="full-body">
    <div class="d-flex align-items-center justify-content-center title">
        <img src="assets/logo/lastengineers_logo.svg" alt="Logo Last Engineers"> 
        <h1 class="my-3">Last Engineers</h1>       
    </div>
    <div class="reset-password-card">

      <h1 class="my-3">Set New Password</h1>
      
      <form [formGroup]="resetForm" (ngSubmit)="onSubmit()" class="d-flex flex-column align-items-center w-100">
        
        <app-field name="password" [required]="true">
            <input autocomplete="off" formControlName="password" class="dm-text-input" type="text"
                name="password" id="password" placeholder="Enter new password" required>
        </app-field>
        <div class="text-danger" *ngIf="resetForm.get('password')?.invalid && resetForm.get('password')?.touched">
            Password must be at least 10 characters long, contain uppercase, lowercase, number, and special character.
        </div>

        <app-field name="confirmPassword" [required]="true">
            <input autocomplete="off" formControlName="confirmPassword" class="dm-text-input" type="text"
                name="confirmPassword" id="confirmPassword" placeholder="Enter new confirmPassword" required>
        </app-field>
        <div class="text-danger" *ngIf="resetForm.hasError('mismatch') && resetForm.get('confirmPassword')?.touched">
            Passwords do not match.
          </div>
        
        <button class="btn btn-primary px-4 my-3" type="submit" [disabled]="resetForm.invalid">
          Reset Password
        </button>
      </form>
      
      <div class="links mt-5">
        <a class="btn btn-primary px-4" routerLink="/login">Back to Login</a>
      </div>
    </div>
    <app-spinner-html *ngIf="isLoading"></app-spinner-html>
</div>
<app-messages></app-messages>