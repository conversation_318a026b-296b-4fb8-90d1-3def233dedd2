import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { PasswordService } from './password.service';
import { MessagesService } from 'src/app/shared/messages/messages.service';
import { ValidatePassword } from 'src/app/shared/validators/password.validator';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {
  resetForm: FormGroup;
  token: string = '';
  isLoading = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private passwordService: PasswordService,
    private messagesService: MessagesService,
  ) {

    this.resetForm = new FormGroup({
      password: new FormControl('', {
        validators: [Validators.required, ValidatePassword],
        nonNullable: true
      }),
      confirmPassword: new FormControl('', {
        validators: [Validators.required],
        nonNullable: true
      })
    }, { validators: [this.formGroupValidator] });
  }

  ngOnInit(): void {
    localStorage.removeItem('token');
    this.token = this.route.snapshot.paramMap.get('token') || '';
  }
  formGroupValidator: ValidatorFn = (
    formGroup: AbstractControl
  ): ValidationErrors | null => {
    const password = formGroup.get('password')?.value;
    const confirmPassword = formGroup.get('confirmPassword')?.value;

    return password === confirmPassword ? null : { mismatch: true };
  };

  onSubmit(): void {

    if (this.resetForm.invalid) {
      return;
    }
    this.isLoading = true
    const password = this.resetForm.get('password')?.value;

    this.passwordService.resetPassword(this.token, password).subscribe({
      next: (response) => {
        this.messagesService.set("Your password changed successfully", 'green')
        setTimeout(() => {
          this.router.navigate(['/login']);
        }, 3000);
      },
      error: (error) => {
        this.isLoading = false
        this.messagesService.set(error, 'error')
      }
    });
  }
}