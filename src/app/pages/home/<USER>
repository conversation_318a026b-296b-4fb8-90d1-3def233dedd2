@import "../../../styles/variables";

.full-body{
    overflow: hidden;
}
.home-container {
    width: 100%;
    height: 100%;
    z-index: 2;
    display: grid;
    grid-template-rows: 100%;
    grid-template-columns: 200px auto;

    .side-image{
        background-image: url('/assets/images/login/login-sidebar.png');
        border-radius: 0.25rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        .white-logo {
            width: 60%;
            margin-top: 2rem;
        }
    }
    .main-content{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
    }

    .btn-home {
        border: 0;
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        box-shadow: 0px 4px 6px -2px rgba(0, 0, 0, 0.5);
        background-color: rgba($dark, $alpha: 0.05);
        color: $dark;

        &>svg,img {
            margin-right: 1rem;
        }

        &:hover {
            background-color: $primary;
            color: white;
            &>svg path{
                fill: #ffffff;
            }
        }

        &.btn-admin {
            top: 1rem;
            position: absolute;
            right: 4rem;
            &:hover {
                background-color: $danger;
                color: $dark;
            }
        }

        &.btn-logout {
            background-color: $dark;
            color: $white;

            &:hover {
                background-color: $primary;
            }
        }
    }

    .btn-module {
        border: 0;
        padding: 1rem 1rem;
        border-radius: 0.5rem;
        background-color: $primary;
        font-size: 1.5rem;
        color: white;
        width: 100%;
        margin: 1rem;
        transition: box-shadow 150ms ease-in;

        &:hover {
            box-shadow: 0px 6px 6px -2px rgba(0, 0, 0, 0.5);
        }

        &>img {
            margin-right: 1rem;
        }
    }

    .activities {
        p {
            font-size: 1rem;
        }
    }

    h5.title {
        font-weight: 700;
    }

}
.home-page-title {
    display: flex;
    justify-content: space-between;
    border: 1px solid $dark;
    border-radius: 1rem;
    align-items: center;
    padding-inline: 2rem;
    padding: 0.5rem 2rem;
    margin-top: 0.5rem;
}

.last-image {
    position: absolute;
    bottom: -2rem;
    right: -1.5rem;
    z-index: 1;
}