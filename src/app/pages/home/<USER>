import { Component, OnDestroy, OnInit, signal } from '@angular/core';
import { Subscription } from 'rxjs';
import { TicketService } from 'src/app/services/ticket.service';
import { UserService } from 'src/app/services/user.service';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit, OnDestroy {
  stats: any = {
    "inventory": 0,
    "average": 0
  }
  isAdmin = signal<boolean>(false);
  unreadTickets = signal<number>(0);
  subscriptions: Subscription[] = [];

  constructor(
    public UserService: UserService,
    private SpinnerService: SpinnerService,
    private ticketService: TicketService
  ) { }

  ngOnInit(): void {
    this.isAdmin.set(this.UserService.isUserAdmin);
    // this.UserService.getStats().subscribe({
    //   next: (response: any) => {
    //     this.stats = response;
    //     this.SpinnerService.globalSpinnerSubject.next(false);
    //   },
    //   error: (err) => {
    //     console.log(err);
    //     this.SpinnerService.globalSpinnerSubject.next(false);
    //   }
    // })

    this.subscriptions.push(this.ticketService.getUnreadTicketsData().subscribe((unreadTicketNumber: any) => {
      this.unreadTickets.set(unreadTicketNumber['ticketCount']);
    }));
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(s => s.unsubscribe());
  }
}