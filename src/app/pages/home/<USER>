<div class="full-body">
    <div class="home-container">
        <div class="position-relative side-image">
            <img class="white-logo" src="assets/logo/lastengineers_logo-white.svg" alt="last engineers white logo">
        </div>
        <div class="main-content">
            <div class="d-flex justify-content-around align-items-center my-3">
                <div class="home-page-title">
                    <img src="assets/icons/home-icon.svg" width="30px" alt="Home Icon">
                    <h5 class="ms-3 mt-2 fw-bold">{{ 'Homepage' | translate }}</h5>
                </div>

                <button [routerLink]="['/admin']"
                    class="btn-home btn-admin pointer">
                    <img src="assets/icons/admin-icon.svg" alt="Admin Icon">
                    {{ 'Admin' | translate }}
                    <app-notification [unreadTickets]="unreadTickets()"></app-notification>
                </button>
            </div>
            <section class="d-flex flex-column align-items-center w-50">
                <h3 class="w-100 text-left border-bottom">
                    {{ 'My Account' | translate }}
                </h3>
                <div class="w-100 d-flex align-items-center justify-content-around pt-3">
                    <button [routerLink]="['/standards']" class="btn-home pointer px-4">
                        <!-- <img src="assets/icons/hand-scanner-icon.svg" alt="Scanner Icon"> -->
                        <svg width="17" height="20" viewBox="0 0 17 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17 5.84375C17 2.60313 14.3969 0 11.1562 0H2.125C2.07188 0 2.01875 0 1.96562 0C1.9125 0 1.85937 0 1.80625 0C0.85 0 0 0.85 0 1.85938V6.10937C0 7.11875 0.85 7.96875 1.85938 7.96875C1.9125 7.96875 1.96563 7.96875 2.01875 7.96875C2.07188 7.96875 2.125 7.96875 2.17813 7.96875H7.49063V8.925C7.49063 9.5625 7.225 10.1469 6.8 10.5719C6.53437 10.8375 6.42813 11.1562 6.42813 11.475C6.42813 12.1656 7.0125 12.75 7.70313 12.75H9.775L10.8375 15.8844C10.8906 16.0438 10.8375 16.15 10.7844 16.2562C10.625 16.4156 10.5188 16.4688 10.3594 16.4688C9.61563 16.4688 9.03125 17.0531 9.03125 17.7969C9.03125 18.5406 9.61563 19.125 10.3594 19.125H15.0875C16.15 19.125 17 18.275 17 17.2125C17 17 16.9469 16.7875 16.8937 16.6281L14.025 7.96875H14.875C16.0438 7.96875 17 7.0125 17 5.84375ZM11.1562 1.0625C13.2281 1.0625 14.9813 2.39062 15.6719 4.25H7.4375C5.57813 4.25 4.0375 2.86875 3.77188 1.0625H11.1562ZM2.65625 6.10937C2.65625 6.48125 2.39062 6.8 1.96562 6.90625C1.43437 6.85312 1.00937 6.375 1.00937 5.84375V2.125C1.00937 1.59375 1.43437 1.11562 1.96562 1.0625C2.39062 1.11562 2.65625 1.4875 2.65625 1.85938V6.10937ZM7.65 11.6875C7.54375 11.6875 7.4375 11.5812 7.4375 11.475C7.4375 11.4219 7.4375 11.3687 7.49063 11.3156C8.075 10.7312 8.44688 9.93437 8.5 9.08437L9.35 11.6875H7.65ZM15.8844 16.9469C15.8844 17.0531 15.9375 17.1062 15.9375 17.2125C15.9375 17.6906 15.5656 18.0625 15.0875 18.0625H10.3594C10.2 18.0625 10.0938 17.9562 10.0938 17.7969C10.0938 17.6375 10.2 17.5312 10.3594 17.5312C10.8375 17.5312 11.2625 17.3187 11.5813 16.8937C11.8469 16.5219 11.9531 15.9906 11.7938 15.5656L10.625 12.1125C10.625 12.0594 10.625 12.0594 10.5719 12.0063L9.24375 7.96875H12.9094L15.8844 16.9469ZM3.55938 6.90625C3.66563 6.64062 3.71875 6.375 3.71875 6.10937V3.50625C4.62188 4.56875 5.95 5.3125 7.4375 5.3125H15.8844C15.8844 5.47187 15.9375 5.68437 15.9375 5.84375C15.9375 6.42812 15.4594 6.90625 14.875 6.90625H3.55938Z" fill="#212529"/>
                            </svg>
                        {{ 'Standard' | translate }}
                    </button>
                    <a [routerLink]="['/support']" class="btn-home pointer px-4">
                        <svg width="17" height="13" viewBox="0 0 17 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M2.12516 0.125H14.8752C15.6576 0.125 16.2918 0.759263 16.2918 1.54167V11.4583C16.2918 12.2407 15.6576 12.875 14.8752 12.875H2.12516C1.34276 12.875 0.708496 12.2407 0.708496 11.4583V1.54167C0.708496 0.759263 1.34276 0.125 2.12516 0.125ZM2.12516 4.81283V11.4583H14.8752V4.81313L8.50015 8.00064L2.12516 4.81283ZM2.12516 3.22891L8.50018 6.41674L14.8752 3.22925V1.54167H2.12516V3.22891Z" fill="#212529"/>
                            </svg>
                        Support
                    </a>
                    <button (click)="UserService.logout()" class="btn-home btn-logout pointer px-4">
                        <img src="assets/icons/out-icon-white.svg" alt="Scanner Icon">
                        {{ 'Log Out' | translate }}
                    </button>
                </div>
            </section>

            <section class="d-flex flex-column align-items-center w-50 mt-5 activities">
                <h3 class="w-100 text-left border-bottom">
                    {{ 'Activity' | translate }}
                </h3>
                <div class="row w-100 py-4">
                    <div class="col text-center">
                        <p>{{ 'Invented' | translate }}: {{stats.inventory}}</p>
                        <p>{{ 'Analyse' | translate }}: {{stats.average }}</p>
                        <p>{{ 'Optimization' | translate }}: {{stats.average }}</p>
                    </div>
                    <div class="col text-center">
                        <p>{{ 'Modification' | translate }}: {{stats.inventory}}</p>
                        <p>{{ 'Comparison' | translate }}: {{stats.average }}</p>
                    </div>
                    <div class="col text-center">
                        <p>{{ 'Last Connexion' | translate }} : {{ '10:49:09 30/05/2025' }}</p>
                    </div>
                </div>
            </section>

            <section class="d-flex flex-column align-items-center w-50 mt-3">
                <h3 class="w-100 text-left border-bottom">
                    {{ 'Modules' | translate }}
                </h3>
                <div class="row pt-3">
                    <div class="col-4 d-flex justify-content-center">
                        <button [routerLink]="['/last-inventory']" class="btn-module pointer ">
                            <img src="assets/icons/database-icon.svg" width="20px" alt="Inventory icon"> 
                            {{ 'Inventory' | translate }}
                        </button>
                    </div>
                    <div class="col-4 d-flex justify-content-center">
                        <button [routerLink]="['/last-analyse']" class="btn-module pointer ">
                            <img src="assets/icons/data-analytics-icon.svg" width="20px" alt="Foor Averaging icon"> 
                            {{ 'Analyse' | translate }}
                        </button>
                    </div>
                    <div class="col-4 d-flex justify-content-center">
                        <button [routerLink]="['/last-optimization']" class="btn-module pointer ">
                            <img src="assets/icons/data-analytics-icon.svg" width="20px" alt="Data Analytics icon"> 
                            {{ 'Optimization' | translate }}
                        </button>
                    </div>
                    <div class="col-4 d-flex justify-content-center">
                        <button [routerLink]="['/last-modification']" class="btn-module pointer">
                            <img src="assets/icons/fitting-icon.svg" width="30px" alt="Inventory icon">
                            {{ 'Modification' | translate }}
                        </button>
                    </div>
                    <div class="col-4 d-flex justify-content-center">
                        <button [routerLink]="['/last-comparison']" class="btn-module pointer">
                            <img src="assets/icons/fitting-icon.svg" width="30px" alt="Inventory icon">
                            {{ 'Comparison' | translate }}
                        </button>
                    </div>
                    <div class="col-4 d-flex justify-content-center">
                        <button [routerLink]="['/last-data-analytics']" class="btn-module pointer ">
                            <img src="assets/icons/data-analytics-icon.svg" width="20px" alt="Data Analytics icon"> 
                            {{ 'Data Analytics' | translate }}
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </div>
    <img src="assets/images/home/<USER>" alt="Green Last Image" class="last-image">
</div>