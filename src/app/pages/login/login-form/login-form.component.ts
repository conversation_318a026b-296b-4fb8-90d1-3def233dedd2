import { animate, style, transition, trigger } from '@angular/animations';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { RecaptchaComponent } from 'ng-recaptcha';
import { LocalService } from 'src/app/services/local.service';
import { UserService } from 'src/app/services/user.service';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-login-form',
  templateUrl: './login-form.component.html',
  styleUrls: ['./login-form.component.scss'],
  animations: [
    trigger('loginState', [
      transition(':enter', [
        style({
          transform: 'translateY(-160%)'
        }),
        animate('300ms ease-in')
      ]),
      transition(':leave', [
        animate('300ms ease', style({
          transform: 'translateY(-160%)'
        }))
      ])
    ])
  ]
})
export class LoginFormComponent implements OnInit {
  siteKey: string = environment.recaptcha.siteKey
  loginForm: FormGroup;
  type: string = 'password';

  constructor(private UserService: UserService,
    private LocalService: LocalService,
    private router: Router,
    private SpinnerService: SpinnerService) {

  }

  ngOnInit() {
    RecaptchaComponent.prototype.ngOnDestroy = function () { };
    this.loginForm = new FormGroup({
      lastengineers_email: new FormControl(null, Validators.required),
      lastengineers_password: new FormControl(null, Validators.required),
      recaptcha: new FormControl(null, Validators.required)
    });
    Promise.resolve().then(()=>{
      this.SpinnerService.hide()
    })
  }
  onLogin(){
    this.LocalService.clear();
    if(this.loginForm.valid){
      this.SpinnerService.show()
      let data = {
        username: this.loginForm.get('lastengineers_email')?.value,
        password: this.loginForm.get('lastengineers_password')?.value,
        recaptcha: this.loginForm.get('recaptcha')?.value,
      }
      this.loginForm.get('recaptcha')?.reset()
      this.UserService.login(data).subscribe({
        next : (response: any ) => {
          localStorage.setItem('token', response.token)
          this.router.navigateByUrl('/');
          this.SpinnerService.hide();
        },
        error: (err) => {
          this.SpinnerService.hide()
        },
      })
    }
  }
  toggle_password(){
    this.type = this.type === 'password' ? 'text' : 'password';    
  }
}