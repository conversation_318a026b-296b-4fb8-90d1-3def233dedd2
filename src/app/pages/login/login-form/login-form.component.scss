@import "../../../../styles/variables"; 
.login-form{
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    z-index: 20;
    top: 0;
    background-color: rgba($dark, $alpha: 0.3);
    &>.form-container{
        width: 50%;
        height: 50%;
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0px 4px 6px -2px rgba(0, 0, 0, 0.5);
        padding: 1rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        &>.title{
            color: $primary;
            width: 100%;
            text-align: center;
            font-weight: 700;
        }
        &>form{
            padding: 2rem 0;
            width: 60%;
            text-align: center;
            &>.actions{
                margin: 3rem 0 0 0;
                p{
                    margin: 0;
                    font-size: 0.9rem;
                    &>a{
                        font-size: 0.9rem;
                    }
                }
            }
            app-field{
                position: relative;
                .show-hide{
                    position: absolute;
                    width: 20px;
                    top: 50%;
                    right: 0;
                    transform: translate(-50%,-50%);
                }
            }
        }
    }
}