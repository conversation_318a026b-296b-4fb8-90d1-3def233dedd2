@import "../../../styles/variables"; 
.login-container{
    width: 100vw;
    height: 100vh;
    display: flex;
    .white-logo{
        position: absolute;
        top: 2.5rem;
        left: 50%;
        transform: translateX(-50%);
        width: 50%;
    }
    .main-section{
        position: relative;
        overflow: hidden;
        &>*{
            transition: all 800ms ease-in;
        }
        &>h1{
            margin-left: 2rem;
            font-weight: 700;
            font-size: 3rem;
            opacity: 0;
        }
        &>img{
            position: absolute;
        }
        &>.image-1{
            top: 0;
            right: 1rem;
            transform: translateY(-100%);
        }
        &>.image-2{
            right: 0;
            bottom: 20%;
            transform: translateX(100%);
        }
        &>.image-3{
            bottom: 0;
            right: 400px;
            transform: translateY(100%);
        }
        &.animate{
            &>.image-1{
                transform: translateY(0);
            }
            &>.image-2{
                transform: translateX(0);
            }
            &>.image-3{
                transform: translateY(0);
            }
            &>h1{
                opacity: 1;
            }
        }
        &>.copyright{
            position: absolute;
            bottom: 0;
            right: 0;
            color: rgba($dark, 0.4);
            font-size: 1.2rem;
            padding: 0.2rem 0.5rem;
            margin: 0;
        }
    }
    
}