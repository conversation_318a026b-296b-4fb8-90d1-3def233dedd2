import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { search_mesures_object } from 'src/app/shared/enums/mesures';

interface SearchConfig {
  filter:any;
  page:number; 
  sort?: string; 
  sortDirection?: string
}

@Injectable({
  providedIn: 'root'
})
export class SearchService {

  open_search:Subject<boolean> = new Subject<boolean>();
  last_list_changed:Subject<any> = new Subject<any>();
  search:Subject<SearchConfig> = new Subject<SearchConfig>();
  isSearch:boolean = false;
  filters:any = {}
  lasts_list: any[] = []
  constructor() { }

  getFormattedFilters() {
    let formated_filters:any = {};

    if(this.filters.min_size != undefined || this.filters.max_size != undefined){
      formated_filters.size = this.filters.system_size.slice(0,2).toUpperCase();
      if(this.filters.min_size != undefined){
        formated_filters.size = this.filters.min_size + ' < ' + formated_filters.size
      }
      if(this.filters.max_size != undefined){
        formated_filters.size = formated_filters.size + ' < ' +  this.filters.max_size
      }
    }
    if(this.filters.min_age != undefined || this.filters.max_age != undefined){
      formated_filters.age = 'Age';
      if(this.filters.min_age != undefined){
        formated_filters.age = this.filters.min_age + ' < ' + formated_filters.age
      }
      if(this.filters.max_age != undefined){
        formated_filters.age = formated_filters.age + ' < ' +  this.filters.max_age
      }
    }

    if(this.filters.min_height != undefined || this.filters.max_height != undefined){
      formated_filters.height = 'Height';
      if(this.filters.min_height != undefined){
        formated_filters.height = this.filters.min_height + ' < ' + formated_filters.height
      }
      if(this.filters.max_height != undefined){
        formated_filters.height = formated_filters.height + ' < ' +  this.filters.max_height
      }
    }

    if(this.filters.min_weight != undefined || this.filters.max_weight != undefined){
      formated_filters.weight = 'Weight';
      if(this.filters.min_weight != undefined){
        formated_filters.weight = this.filters.min_weight + ' < ' + formated_filters.weight
      }
      if(this.filters.max_weight != undefined){
        formated_filters.weight = formated_filters.weight + ' < ' +  this.filters.max_weight
      }
    }

    if(this.filters.Last != undefined){
      formated_filters.Last = this.filters.Last;
    }
    if(this.filters.reference != undefined){
      formated_filters.reference = this.filters.reference;
    }
    if(this.filters.gender != undefined){
      this.getMultipleOptions('gender', this.filters.gender, formated_filters);
    }
    if(this.filters.category != undefined){
      this.getMultipleOptions('category', this.filters.category, formated_filters);
    }
    if(this.filters.countries != undefined){
      this.getMultipleOptions('countries', this.filters.countries, formated_filters);
    }
    if(this.filters.scanners != undefined){
      this.getMultipleOptions('scanners', this.filters.scanners, formated_filters);
    }
    if(this.filters.lasttype != undefined){
      this.getMultipleOptions('lasttype', this.filters.lasttype, formated_filters);
    }
    if(this.filters.archtype != undefined){
      this.getMultipleOptions('archtype', this.filters.archtype, formated_filters);
    }
    if(this.filters.direction != undefined){
      this.getMultipleOptions('direction', this.filters.direction, formated_filters);
    }
    if(this.filters.measures_group != undefined){
      Object.keys(this.filters.measures_group).forEach(element => {
        formated_filters['measures_group.' +element] = search_mesures_object[element];
        if(this.filters.measures_group[element].min){
          formated_filters['measures_group.' +element]= this.filters.measures_group[element].min + ' < ' + formated_filters['measures_group.' +element]
        }
        if(this.filters.measures_group[element].max){
          formated_filters['measures_group.' +element]= formated_filters['measures_group.' +element] + ' < ' +  this.filters.measures_group[element].max
        }        
      });
    }
    
    return formated_filters;
  }

  getMultipleOptions(filter: string, options: string[], formated_filters: any) {
    return options.forEach((c: string, i: number) => formated_filters[`${filter}-${i + 1}`] = c);
  }
}
