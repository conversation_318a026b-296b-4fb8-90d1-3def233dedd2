import { Injectable } from '@angular/core';
import { HttpClient } from "@angular/common/http";
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class StaticDataService {

  shoetypes: any[] = [];
  standards: any[] = [];
  fields: any[] = [];
  genders: any[] = [];
  categories: any[] = [];
  roles: any[] = [];
  system_sizes: any[] = [];
  system_size: string = 'eu';
  height_types : any[] = [];
  ferrato_types : any[] = [];
  last_bottom_lock_options : any[] = [];
  toe_shape_types : any[] = [];
  units : any[] = [];
  file_qualities : any[] = [];
  orientations : any[] = [];
  wide_width_options : any = {};


  constructor(
    private http: HttpClient,
    private TranslateService: TranslateService) { }

  getStaticData() {
    return this.http.get(environment.api_url + 'api/' + this.lang + '/reference/all');
  }
  setStaticData(response: any) {
    debugger
    this.categories = [...response.categories]
    this.standards = [...response.standards]
    this.genders = [...response.genders]
    // this.roles = [...response.roles]
    this.system_sizes = [...response.system_sizes]
    this.system_size = response.system_size
    this.fields = response.fields
    this.height_types = response.height_types
    this.ferrato_types = response.ferrato_types
    this.last_bottom_lock_options = response.last_bottom_lock_options
    this.toe_shape_types = response.toe_shape_types
    this.units = response.units
    this.file_qualities = response.file_qualities
    this.orientations = response.orientations
    this.wide_width_options = response.wide_width_options
  }
  get lang() {
    return this.TranslateService.getDefaultLang() ?? 'en'
  }
}
