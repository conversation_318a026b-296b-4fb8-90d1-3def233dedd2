import { Injectable } from '@angular/core';
import { TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CustomTranslateLoader implements TranslateLoader {
  constructor(private http: HttpClient) { }

  getLanguages(): Observable<any> {
    return this.http.get<any>(environment.api_url + 'api/' + this.lang + '/translation/languages');
  }

  getAllTranslations(): Observable<any> {
    return this.http.get<any>(environment.api_url + 'api/' + this.lang + '/translation/all');
  }

  createNewTranslation(data: any): Observable<any> {
    return this.http.post(environment.api_url + 'api/' + this.lang + '/translation/new', data);
  }

  updateLanguages(data: any): Observable<any> {
    return this.http.put(environment.api_url + 'api/' + this.lang + '/translation/update', data);
  }

  getTranslation(lang: string): Observable<any> {
    return this.http.get<any>(`${environment.api_url}api/${this.lang}/translation/${lang}`);
  }

  get lang() {
    return localStorage.getItem('lang') ?? 'en';
  }
}
