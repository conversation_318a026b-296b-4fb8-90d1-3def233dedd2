import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from "@angular/common/http";
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from './user.service';

@Injectable({
  providedIn: 'root'
})
export class FileService {

  constructor(private http: HttpClient,
              private UserService: UserService,
              private TranslateService: TranslateService) { }

  getDownloadStl(id:string, type:string = 'test'){
    let headers = new HttpHeaders();

    let url = environment.api_url + 'api/'+ this.lang +'/lasts/' + id + '.stl'

    headers = headers.set('Accept', 'application/STL');
    return this.http.get(url ,{ headers: headers, responseType: 'blob' });
  }
  getDownloadPdf(id:string, type:string = 'test', data:any){
    let headers = new HttpHeaders();
    let url = environment.api_url + 'api/'+ this.lang +'/pdf/last/' + id + '.pdf?';
    headers = headers.set('Accept', 'application/pdf'); 
    // return this.http.post( url + new Date().getTime().toString() , data ,{ headers: headers, responseType: 'blob' });
    return this.http.get( url + new Date().getTime().toString() ,{ headers: headers, responseType: 'blob' });
  }
  getDownloadExcel(type: string) {
    let url = environment.api_url + 'api/'+ this.lang +'/excel/' + type
    return this.http.get(url, { responseType: 'blob' });
  }
  downloadFile(data: any, name:string) {
    const blob = new Blob([data], { type: 'octet/stream' });
    let a = document.createElement('a');
    document.body.appendChild(a);
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = name;
    a.click();
    window.URL.revokeObjectURL(url);
  }
  getStlMesures(id:string){
    let url = environment.api_url + 'api/'+ this.lang +'/lasts/mesures/' + id;
    return this.http.get(url)
  }

  getLastMesures(ref:string){
    let url = this.UserService.user.linked_platform + '/lastengineers/last/' + ref + '.mesure';
    return this.http.get(url)
  }

  getDownloadLastStl(id:number, type:string){
    let headers = new HttpHeaders();

    let url = environment.api_url + 'api/'+ this.lang +'/last_last/stl/' + id + '_' + type + '_last.stl';
    headers = headers.set('Accept', 'application/STL');
    return this.http.get(url ,{ headers: headers, responseType: 'blob' });
  }

  get lang(){
    return this.TranslateService.getDefaultLang() ?? 'en'
  }
}
