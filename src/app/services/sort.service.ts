import { Injectable, signal } from "@angular/core";
import { SortDirectionEnum } from "../enums/columns-sorting";

// Sort Config Interface
export interface SortConfig {
    sort: string;
    sortDirection: SortDirectionEnum;
}

// Initial Sort Config
export const initialSortConfig: SortConfig = {
    sort: 'createdAt', 
    sortDirection: SortDirectionEnum.DESCENDANT
};

@Injectable({
    providedIn: 'root'
})

export class SortService {
    sortConfig = signal<SortConfig>(initialSortConfig);

    getSortConfig() {
        return this.sortConfig();
    }

    setSortConfig(sort: string, sortDirection = SortDirectionEnum.DESCENDANT) {
        this.sortConfig.set({sort, sortDirection});
    }
}
