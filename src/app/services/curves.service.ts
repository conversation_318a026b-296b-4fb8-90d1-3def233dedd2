import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CurvesService {
  toggleMesure: Subject<{ slug: string }> = new Subject<{ slug: string }>();
  curve_status: Subject<{ slug: string, status: boolean }> = new Subject<{ slug: string, status: boolean }>();
  image: string | null = null;
  constructor() { }

  getMeasuresGroupKey(type: string) {
    switch (type) {
      case '':
        return 'Lengths (mm)'
      case 'Curve':
        return 'Perimeters (mm)'
      case 'Angle':
        return 'Angles (°)'
      case 'vertexGroup':
        return 'Areas (mm²)'
      case 'ground':
        return 'Others'
      default:
        return '' + type + 's (mm)'
    }
  }
}
