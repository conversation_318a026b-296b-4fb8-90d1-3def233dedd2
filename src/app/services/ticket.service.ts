import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from './user.service';
import { SortService } from './sort.service';
import { BehaviorSubject } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class TicketService {
    private unreadTicketsSubject = new BehaviorSubject<any>({});
    private adminAsUserunreadTicketsSubject = new BehaviorSubject<any>({});
    translateService = inject(TranslateService);
    http = inject(HttpClient);
    userService = inject(UserService);
    sortService = inject(SortService);

    getTicketsByPage$(page = 0, isAdminPath: boolean) {
        const pageSize: any = this.userService.user.items_number;

        return this.http.post(environment.api_url + 'api/' + this.lang + '/ticket/search', { ...this.sortService.getSortConfig(), isAdminPath }, {
            params: new HttpParams().set('pageSize', pageSize).set('page', page)
        });
    }

    addNewTicket$(data: any) {
        return this.http.post(environment.api_url + 'api/' + this.lang + '/ticket/new', data);
    }

    closeTicket$(id: any, isAdminPath: boolean) {
        return this.http.put(environment.api_url + 'api/' + this.lang + '/ticket/' + id + '/close', { isAdminPath });
    }

    addMessageToTicket$(id: any, requestData: any) {
        return this.http.post(environment.api_url + 'api/' + this.lang + '/ticket/' + id + '/add-message', { ...requestData });
    }

    showMessageAttachments$(id: number) {
        return this.http.get(environment.api_url + 'api/' + this.lang + '/ticket/' + id + '/show-attachments');
    }

    getUnreadTicketsData() {
        return this.unreadTicketsSubject.asObservable();
    }

    getAdminAsUserUnreadTicketsData() {
        return this.adminAsUserunreadTicketsSubject.asObservable();
    }

    setUnreadTicketsData(unreadTicketsData: any) {
        this.unreadTicketsSubject.next(unreadTicketsData);
    }

    setAdminAsUserUnreadTicketsData(unreadTicketsData: any) {
        this.adminAsUserunreadTicketsSubject.next(unreadTicketsData);
    }

    get lang() {
        return this.translateService.getDefaultLang();
    }
}
