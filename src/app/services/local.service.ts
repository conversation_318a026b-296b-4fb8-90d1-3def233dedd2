import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LocalService {
  open_fullscreen: Subject<boolean> =  new Subject<boolean>();
  open_info:Subject<boolean> = new Subject<boolean>();
  open_custom_table:Subject<{open: boolean, isAverage?: boolean}> = 
  new Subject<{open: boolean, isAverage?: boolean}>();
  
  selected_last: any = null;
  select_subject: Subject<any> = new Subject<any>();
  selected_last_data: any = null;

  selected_last_data_arrived: Subject<any> = new Subject<any>();

  constructor() { }
  selectLast(select_last: any) {
    this.selected_last = select_last
    this.select_subject.next(select_last);
  }
  addData(select_last_data: any) {
    this.selected_last_data = select_last_data
    this.selected_last_data_arrived.next(this.selected_last_data);
  }
  clear(){
    this.selected_last = null
    this.selected_last_data = null
  }

}