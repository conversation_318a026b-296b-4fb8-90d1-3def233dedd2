// translation.service.ts
import { inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { MessagesService } from '../shared/messages/messages.service';
import { SpinnerService } from '../shared/spinner/spinner.service';

@Injectable({
    providedIn: 'root',
})
export class CustomTranslateService {
    private languageSubject = new BehaviorSubject<{ name: string, slug: string }[]>([]);
    private languageLoadedSubject = new Subject<void>();

    translate = inject(TranslateService);
    spinner = inject(SpinnerService);
    alert = inject(MessagesService);

    getLanguagesName() {
        return this.languageSubject.asObservable();
    }

    setLanguagesName(languages: { name: string, slug: string }[]) {
        this.languageSubject.next(languages);
    }

    get languageLoaded$() {
        return this.languageLoadedSubject.asObservable();
    }

    setLanguage(language: string, data?: {message:string}): void {
        this.translate.use(language).subscribe({
            next: () => {
                localStorage.setItem('lang', language);
                this.translate.setDefaultLang(language);
                this.languageLoadedSubject.next();
                if(data) {
                    this.alert.set(data.message, 'green');
                    this.spinner.globalSpinnerSubject.next(false);
                }
            },
            error: (error) => {
                this.spinner.globalSpinnerSubject.next(false);
                this.alert.set(error.message, 'error');
            }
        });
    }
}
