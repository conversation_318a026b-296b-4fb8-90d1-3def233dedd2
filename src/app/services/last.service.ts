import { Injectable } from '@angular/core';
import {HttpClient, HttpParams} from "@angular/common/http";
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { UserService } from './user.service';
import { SortService } from './sort.service';

@Injectable({
  providedIn: 'root'
})
export class LastService {
  
  constructor(private http: HttpClient,
              private userService: UserService,
              private TranslateService: TranslateService,
              private sortService: SortService) { }

  createNewLast(data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/lasts/new', data);
  }
  updateLast(ref: any, data:any){
    return this.http.post(environment.api_url + 'api/'+ this.lang +'/lasts/update/' + ref, data);
  }
  getLastsByPage(page: number = 0){
    const pageSize : any = localStorage.getItem('pageSize') ? localStorage.getItem('pageSize') : '15';

    return this.http.get(environment.api_url + 'api/'+ this.lang +'/lasts/all',{
      params: new HttpParams().set('pageSize', pageSize).set('page', page)
    });
  } 
  deleteLast(ref: any) {
    return this.http.delete(environment.api_url + 'api/' + this.lang + '/lasts/' + ref);
  }
  getOneLast(ref: any) {
    return this.http.get(environment.api_url + 'api/' + this.lang + '/lasts/edit/' + ref);
  }
  // getSearchByPage(filter:any ,page: number = 0){
  //   const pageSize : any = this.userService.user.items_number;

  //   return this.http.post(environment.api_url + 'api/'+ this.lang +'/lasts/search', { ...filter, ...this.sortService.getSortConfig() }, {
  //     params: new HttpParams().set('pageSize', pageSize).set('page', page)
  //   });
  // }

  getActiveStandardsByCompany(){
    // /api/active/standards
    return this.http.get(environment.api_url + 'api/' + this.lang + '/active/standards');
  }
  getSearchByPage(){

    let httpParams = new HttpParams();

    httpParams.append('limit', this.userService.user.parameters.pagination_size);

    return this.http.get(environment.api_url + 'api/'+ this.lang +'/lasts/list', { params : httpParams});
  }
  get lang(){
    return this.TranslateService.getDefaultLang() ?? 'en'
  }
}
