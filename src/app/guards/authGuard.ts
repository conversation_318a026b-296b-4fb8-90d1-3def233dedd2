import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, Router, CanActivateChildFn, CanActivateFn } from '@angular/router';
import { map } from 'rxjs';
import { UserService } from '../services/user.service';
import { SpinnerService } from '../shared/spinner/spinner.service';
import { StaticDataService } from '../services/static.service';
import { CustomTranslateService } from '../services/custom-translate-service.service';


export const canActivateAuth: CanActivateFn = (
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
) => {
    const userService = inject(UserService);
    const spinnerService = inject(SpinnerService);
    const router = inject(Router);
    const StaticData = inject(StaticDataService);
    const customTranslateService = inject(CustomTranslateService);
    spinnerService.globalSpinnerSubject.next(true);
    return userService.isAuthenticated().pipe(
        map((response: any) => {
            if (state.url != '/home') {
                // customTranslateService.languageLoaded$.subscribe(() => spinnerService.globalSpinnerSubject.next(false));
            }
            debugger
            if (response.data.user) {
                userService.user = response.data.user;
                StaticData.setStaticData(response.data);
                // userService.isUserAdmin = response.data.user['user_role']['slug'] == 'admin';
                return true;
            } else {
                router.navigateByUrl('/login');
                return false;
            }
        }));
};

export const canActivateChildAuth: CanActivateChildFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
    const userService = inject(UserService);
    const spinnerService = inject(SpinnerService);
    const router = inject(Router);
    const StaticData = inject(StaticDataService);

    spinnerService.globalSpinnerSubject.next(true);
    return userService.isAuthenticated().pipe(
        map((response: any) => {
            if (response.data.user) {
                userService.user = response.data.user
                // userService.isUserAdmin = response.data.user['user_role']['slug'] == 'admin';
                StaticData.setStaticData(response.data)
                return true;
            } else {
                router.navigateByUrl('/login');
                return false;
            }
        }));
};
