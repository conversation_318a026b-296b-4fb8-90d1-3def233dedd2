import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, Router, CanActivateChildFn, CanActivateFn } from '@angular/router';
import { map } from 'rxjs';
import { UserService } from '../services/user.service';
import { SpinnerService } from '../shared/spinner/spinner.service';
import { StaticDataService } from '../services/static.service';
import { TicketService } from '../services/ticket.service';
import { TranslateService } from '@ngx-translate/core';
import { CustomTranslateLoader } from '../services/custom-translate-loader.service';
import { CustomTranslateService } from '../services/custom-translate-service.service';
import { AdminService } from '../admin/services/admin.service';

export const canActivateAdmin: CanActivateFn = (
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
) => {
    const userService = inject(UserService);
    const adminService = inject(AdminService);
    const spinnerService = inject(SpinnerService);
    const router = inject(Router);
    const StaticData = inject(StaticDataService);
    const ticketService = inject(TicketService);
    const translateService = inject(TranslateService);
    const customTranslateLoader = inject(CustomTranslateLoader);
    const customTranslateService = inject(CustomTranslateService);
    spinnerService.globalSpinnerSubject.next(true)
    return userService.isAdmin().pipe(
        map((response: any) => {
            userService.user = response.data.user
            adminService.setCompanies(response.companies);
            StaticData.setStaticData(response.data)
            // userService.isUserAdmin = response.data.user['user_role']['slug'] == 'admin';
            // ticketService.setUnreadTicketsData(response.data.unread_tickets);
            // ticketService.setAdminAsUserUnreadTicketsData(response.data.admin_as_user_unread_tickets);
            // const preferedLanguage = response.data.user?.prefered_language ?? customTranslateLoader.lang;
            // translateService.reloadLang(preferedLanguage).subscribe(() => {
            //     customTranslateService.setLanguage(preferedLanguage);
            // });
            if (response.is_admin) {
                return true;
            } else {
                router.navigateByUrl('/home');
                return false;
            }
        }));
};

export const canActivateChildAdmin: CanActivateChildFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
    const userService = inject(UserService);
    const spinnerService = inject(SpinnerService);
    const router = inject(Router);
    const StaticData = inject(StaticDataService);
    const ticketService = inject(TicketService);

    spinnerService.globalSpinnerSubject.next(true)
    return userService.isAdmin().pipe(
        map((response: any) => {
            spinnerService.globalSpinnerSubject.next(false)
            userService.user = response.data.user;
            StaticData.setStaticData(response.data)
            // userService.isUserAdmin = response.data.user['user_role']['slug'] == 'admin';
            // ticketService.setUnreadTicketsData(response.data.unread_tickets);
            // ticketService.setAdminAsUserUnreadTicketsData(response.data.admin_as_user_unread_tickets);
            if (response.is_admin) {
                return true;
            } else {
                router.navigateByUrl('/home');
                return false;
            }
        }));
};