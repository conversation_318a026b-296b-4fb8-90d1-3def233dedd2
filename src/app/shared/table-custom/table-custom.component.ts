import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { LocalService } from 'src/app/services/local.service';
import { SearchService } from 'src/app/services/search.service';
import { SpinnerService } from '../spinner/spinner.service';
import { MessagesService } from '../messages/messages.service';
import { UserService } from 'src/app/services/user.service';
import { ColumnsSortKey } from 'src/app/enums/columns-sorting';
import { SortService } from 'src/app/services/sort.service';

@Component({
  selector: 'app-table-custom',
  templateUrl: './table-custom.component.html',
  styleUrls: ['./table-custom.component.scss'],
  animations: [
    trigger('tableState', [
      state('in', style({
        transform: 'translateX(0)'
      })),
      transition(':enter', [
        style({
          transform: 'translateX(500px)'
        }),
        animate('300ms ease-in')
      ]),
      transition(':leave', [
        animate('300ms ease', style({
          transform: 'translateX(500px)'
        }))
      ])
    ])
  ]
})
export class TableCustomComponent implements OnInit, OnDestroy {
  columnsSortKey = ColumnsSortKey;
  subscriptions: Subscription[] = []
  animation_boolean: boolean = false
  selected_columns: { name: string, slug: string, status: boolean}[] = [] ;

  columns = { 
    inventory: 
    [
      { name: 'Preview', slug: 'image', status: false},
      { name: 'Standard', slug: 'standard', status: false},
      { name: 'Shoe Type', slug: 'reference', status: false},
      { name: 'Gender', slug: 'gender_name', status: false},
      { name: 'Category', slug: 'category_name', status: false},
      { name: 'Reference', slug: 'reference', status: false},
      { name: 'System Size', slug: 'system_size', status: false},
      { name: 'Size', slug: 'size', status: false},
      { name: 'Wide Width', slug: 'wide_width', status: false},
      { name: 'Front Insock Thickness', slug: 'front_insock_thickness', status: false},
      { name: 'Back Insock Thickness', slug: 'back_insock_thickness', status: false},
      { name: 'Toe Shape Type', slug: 'toe_shape_type', status: false},
      { name: 'Heel Height', slug: 'heel_height', status: false},
      { name: 'Last bottom lock option', slug: 'last_bottom_lock_option', status: false},
      { name: 'Unit', slug: 'unit', status: false},
      { name: 'Added Date', slug: 'invented_at', status: false}
  ],
}

  constructor(
    private localService: LocalService,
    private UserService: UserService,
    private SearchService: SearchService,
    private messageService: MessagesService,
    private SpinnerService: SpinnerService,
    private sortService: SortService
  ) { }

  ngOnInit(): void {
    // this.columns.inventory.forEach((column) => {
    //   column.status = this.UserService.user.parameters.selected_columns.some((el:any) => el.slug == column.slug);
    // });
  }

  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe();
    }
  }
  sendSelectedColumns() {
    const columns = this.selected_columns;
    const sort = this.columnsSortKey[columns[columns[0].name == 'Preview' ? 1 : 0].name];
    this.sortService.setSortConfig(sort);
    this.SpinnerService.show();
    this.UserService.postSelectedColumns( this.selected_columns ).subscribe({
      next: (response: any) => {
        this.UserService.user = response.data.user;
        this.SearchService.search.next({ filter: this.SearchService.filters, page: 0 });
        this.localService.open_custom_table.next({open:false});
        this.messageService.set(response.message, 'green');
      },
      error: (error) => {
        this.messageService.set(error.message, 'error')
        this.SpinnerService.hide()
      }
    })
  }

  validateSelection() {
    this.selected_columns = [];

    this.columns.inventory.forEach((column) => {
      if (column.status) {
        const selectedColumn = { name: column.name, slug: column.slug, status: column.status};
        this.selected_columns.push(selectedColumn);
      }
    });
  }
  
  close() {
    this.localService.open_custom_table.next({open:false})
  }
}
