<div class="full-body">
<form (ngSubmit)="onCreateNew()" [formGroup]="add_new_form"
    class="app-new-container w-100 h-100 position-relative pt-4">
    <app-spinner></app-spinner>
    <h1>{{ 'Last Import' | translate }}</h1>
    <section class="h-100 w-50 container section1">
        <div class="w-80 mx-auto">

            <div class="dm-badge">
                <p>{{ 'Ref' | translate }}</p>
                <p>#{{ title === 'Update' ? this.last : 'AUTO_GEN' }}</p>
            </div>

            <div class="d-flex informations">
                <h2>
                    Informations <img class="more-infos" title="More Informations" src="assets/icons/question-mark-icon.svg" alt="More Informations">
                    
                </h2>
                <h5 *ngIf="title === 'Create'">{{ 'Added Date' | translate }} : {{ dateNow | date: 'dd/MM/yyyy'}}</h5>
                <h5 *ngIf="title === 'Update'">{{ 'Added Date' | translate }} : {{ dateNow }}</h5>
            </div>

            <app-field [required]="true"  name="Reference">
                <input autocomplete="off" formControlName="reference" class="dm-text-input" type="text"
                    [placeholder]="'Reference' | translate" name="reference" id="reference">
            </app-field>

            <app-searchable-select [required]="true" [slug]="'ref'"  [placeholder]="selected_standard ? selected_standard.name : 'standard' | translate" name="standard" [list]="StaticDataService.standards"
                (select_event)="select_standard($event)"></app-searchable-select>

            <app-field [required]="true"  name="Gender">
                <div class="dm-radio-input-v1 w-100 ps-4">
                    <ng-container *ngFor="let gender of StaticDataService.genders">
                        <input autocomplete="off" formControlName="gender" type="radio" [value]="gender.slug"
                            name="gender" id="{{ 'gender_' + gender.slug}}" >
                        <label for="{{ 'gender_' + gender.slug}}">{{gender.name | translate }}</label>
                    </ng-container>
                </div>
            </app-field>

            <app-field [required]="true"  name="Category">
                <div class="dm-radio-input-v1 w-100 ps-4">
                    <ng-container *ngFor="let category of StaticDataService.categories">
                        <input autocomplete="off" formControlName="category" type="radio" [value]="category.slug"
                            name="category" id="{{ 'category_' +  category.slug}}">
                        <label for="{{ 'category_' +  category.slug}}">{{category.name | translate }}</label>
                    </ng-container>
                </div>
            </app-field>

            <app-searchable-select [required]="true" [placeholder]="selected_shoetype ? selected_shoetype : 'shoetype' | translate" name="shoetype" [slug]="'ref'" [list]="shoetypes()"
                (select_event)="select_shoetype($event)"></app-searchable-select>


            <app-field [required]="true" unit_width="w-25" width="w-75" name="size">
                <input autocomplete="off" formControlName="size" class="dm-text-input" type="text" inputmode="numeric" pattern="\d*"
                    [placeholder]="'Size' | translate" name="size" id="size">
                <div unit class="dm-radio-input-v1">

                    <ng-container *ngFor="let system_size of StaticDataService.system_sizes">
                        <input formControlName="system_size" name="system_size" [value]="system_size.slug" type="radio" id="{{ system_size.slug }}" >
                        <label style="min-width: 2.5rem;" for="{{ system_size.slug }}">{{ system_size.name }}</label>
                    </ng-container>
                </div>
            </app-field>

            <app-searchable-select [required]="true"  *ngIf="add_new_form.value.system_size"
            [placeholder]="add_new_form.value.wide_width_option ? add_new_form.value.wide_width_option :  'Select Wide Width'" name="Wide Width" slug="value"
            [list]="StaticDataService.wide_width_options[add_new_form.value.system_size]"
                (select_event)="select_wide_width($event)"></app-searchable-select>


            <app-field [required]="true"  name="Front Insock Thickness">
                <input autocomplete="off" formControlName="front_insock_thickness" class="dm-text-input" type="text" inputmode="numeric" pattern="\d*"
                    [placeholder]="'Front Insock Thickness' | translate" name="front_insock_thickness" id="front_insock_thickness">
            </app-field>

            <app-field [required]="true"  name="Back Insock Thickness">
                <input autocomplete="off" formControlName="back_insock_thickness" class="dm-text-input" type="text" inputmode="numeric" pattern="\d*"
                    [placeholder]="'Back Insock Thickness' | translate" name="back_insock_thickness" id="back_insock_thickness">
            </app-field>

            <app-field [required]="true"  unit_width="w-50" width="w-50" name="height">
                <input autocomplete="off" formControlName="height" class="dm-text-input" type="text" inputmode="numeric" pattern="\d*"
                    [placeholder]="'height' | translate" name="height" id="height">
                <div unit class="dm-radio-input-v1">

                    <ng-container *ngFor="let height of StaticDataService.height_types">
                        <input formControlName="height_type" type="radio" id="height_{{ height.slug }}" [value]="height.slug">
                        <label style="min-width: 2.5rem;" for="height_{{ height.slug }}">{{ height.name }}</label>
                    </ng-container>

                </div>
            </app-field>
            
            <app-field [required]="true"  unit_width="w-50" width="w-50" name="ferrato">
                <input autocomplete="off" formControlName="ferrato" class="dm-text-input" type="text" inputmode="numeric" pattern="\d*"
                    [placeholder]="'ferrato' | translate" name="ferrato" id="ferrato">
                <div unit class="dm-radio-input-v1">

                    <ng-container *ngFor="let ferrato of StaticDataService.ferrato_types">
                        <input type="radio" formControlName="ferrato_type" id="{{ ferrato.slug }}" [value]="ferrato.slug" >
                        <label style="min-width: 2.5rem;" for="{{ ferrato.slug }}">{{ ferrato.name }}</label>
                    </ng-container>
                </div>
            </app-field>


            <app-field [required]="true"  name="Last bottom lock option">
                <div class="dm-radio-input-v1 w-100 ps-4">
                    <ng-container *ngFor="let option of StaticDataService.last_bottom_lock_options">
                        <input autocomplete="off" formControlName="last_bottom_lock_option" type="radio" [value]="option.slug"
                            name="last_bottom_lock_option" id="{{ 'last_bottom_lock_option_' + option.slug}}" >
                        <label for="{{ 'last_bottom_lock_option_' + option.slug}}">{{option.name | translate }}</label>
                    </ng-container>
                </div>
            </app-field>            
        </div>

    </section>
    <section class="h-100 w-50 container section2">
        <div class="h-45 w-95 mx-auto position-relative pt-4 custom-fields d-flex flex-column ">
            <app-field [required]="true"  name="Toe Shape Type">
                <div class="dm-radio-input-v1 w-100 ps-4">
                    <ng-container *ngFor="let option of StaticDataService.toe_shape_types">
                        <input autocomplete="off" formControlName="toe_shape_type" type="radio" [value]="option.slug"
                            name="toe_shape_type" id="{{ 'toe_shape_type_' + option.slug}}" >
                        <label for="{{ 'toe_shape_type_' + option.slug}}">{{option.name | translate }}</label>
                    </ng-container>
                </div>
            </app-field> 
            <app-field [required]="true"  name="Orintation">
                <div class="dm-radio-input-v1 w-100 ps-4">
                    <ng-container *ngFor="let option of StaticDataService.orientations">
                        <input autocomplete="off" formControlName="orientation" type="radio" [value]="option.slug"
                            name="orientation" id="{{ 'orientation_' + option.slug}}" >
                        <label for="{{ 'orientation_' + option.slug}}">{{option.name | translate }}</label>
                    </ng-container>
                </div>
            </app-field>

            <app-field [required]="true"  name="Unit">
                <div class="dm-radio-input-v1 w-100 ps-4">
                    <ng-container *ngFor="let option of StaticDataService.units">
                        <input autocomplete="off" formControlName="unit" type="radio" [value]="option.slug"
                            name="unit" id="{{ 'unit_' + option.slug}}" >
                        <label for="{{ 'unit_' + option.slug}}">{{option.name | translate }}</label>
                    </ng-container>
                </div>
            </app-field>

            <app-field [required]="true"  name="File Quality">
                <div class="dm-radio-input-v1 w-100 ps-4">
                    <ng-container *ngFor="let option of StaticDataService.file_qualities">
                        <input autocomplete="off" formControlName="file_quality" type="radio" [value]="option.slug"
                            name="file_quality" id="{{ 'file_quality_' + option.slug}}" >
                        <label for="{{ 'file_quality_' + option.slug}}">{{option.name | translate }}</label>
                    </ng-container>
                </div>
            </app-field>
           
        </div>
        <div class="h-45 w-100 model-3d p-3">
            <div class="w-80 h-15 d-flex informations">
                <span class="design"></span>
                <h2>
                    {{ 'Upload STL' | translate }} <img class="more-infos" title="Accept STL and OBJ Files" src="assets/icons/question-mark-icon.svg" alt="Accept STL and OBJ Files">
                </h2>
            </div>
            <div class="h-85 w-100 d-flex lasts-container">
                <div class="single-last">
                    <div class="scene w-100 h-90 pb-1 pointer position-relative">
                        <app-main-scene></app-main-scene>
                        <div class="icon-div" *ngIf="!stl_last_scene" (click)="uploadStl()">
                            <img class="w-30"  src="assets/icons/upload-icon.svg" alt="upload icon">
                        </div>
                    </div>
                    <span class="mini-btn btn-primary py-0">{{ 'Upload' | translate }}</span>
                    <input autocomplete="off" (change)="lastChange($event, 'stl')" style="display: none;" #stllast
                        name="stl" accept=".stl,.obj" type="file">
                </div>
            </div>
        </div>
        <div class="h-10 w-100 d-flex justify-content-end align-items-center">
            <button class="btn btn-outline-primary px-5" type="button" [routerLink]="['/last-inventory']" >{{ 'Cancel' | translate }}</button>
            <button class="btn btn-primary px-5" type="submit" [disabled]="add_new_form.invalid">{{  'Add' | translate }}</button>
        </div>
    </section>
</form>
</div>