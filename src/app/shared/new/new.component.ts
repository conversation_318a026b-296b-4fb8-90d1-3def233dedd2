import { Component, ElementRef, On<PERSON><PERSON>roy, OnInit, signal, Signal, ViewChild } from '@angular/core';
import { LastService } from 'src/app/services/last.service';
import { SpinnerService } from '../spinner/spinner.service';
import { StaticDataService } from 'src/app/services/static.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import * as ENUMS from '../enums/generale'
import { SceneService } from 'src/app/scene/main/scene.service';
import { Subscription } from 'rxjs';
import { MessagesService } from '../messages/messages.service';
import { LocalService } from 'src/app/services/local.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-new',
  templateUrl: './new.component.html',
  styleUrls: ['./new.component.scss']
})
export class NewComponent implements OnInit, OnD<PERSON>roy {

  @ViewChild('stllast') stllast!: ElementRef;

  dateNow: Date = new Date();

  create_new_field: boolean = false
  isLoading: boolean = false
  subscriptions: Subscription[] = []
  right_last_scene: boolean = false
  stl_last_scene: boolean = false
  stl_last: File | null = null;
  title: "Create" | "Update" = "Create"
  selected_standard: any = null
  selected_shoetype: string
  ENUMS: any = ENUMS;
  add_new_form: FormGroup;
  add_new_field_form: FormGroup;
  last = this.activatedRoute.snapshot.params['Last']
  selected_last:any = null

  shoetypes = signal<any[]>([]);


  constructor(private LastService: LastService,
    private SpinnerService: SpinnerService,
    private SceneService: SceneService,
    private LocalService: LocalService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private MessagesService: MessagesService,
    public StaticDataService: StaticDataService
  ) { }

  ngOnInit(): void {    
    setTimeout(() => {
      this.SpinnerService.hide();
      console.log(this.StaticDataService.standards)
    }, 0);
    this.init_form();
    // if(this.last){
    //   this.getLast();
    // }
    this.subscriptions.push(this.SceneService.converted_file.subscribe({
      next:(file_converted)=>{
        this.stl_last = file_converted.file
      }
    }))
  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }


  select_wide_width($event: any) {
    this.add_new_form.controls['wide_width_option'].setValue($event);
  }

  select_standard($event: any) {
    this.add_new_form.controls['standard'].setValue($event);
    this.selected_standard = this.StaticDataService.standards.find((el) => el.ref === $event );
    if(this.selected_standard){
      this.shoetypes.set(this.selected_standard.shoetypes)
    }
  }
  select_shoetype($event: any) {
    this.add_new_form.controls['shoetype'].setValue($event);
  }

  init_form() {
    this.add_new_form = new FormGroup({
      'reference': new FormControl('', [Validators.required]),
      'standard': new FormControl('', []),
      'gender': new FormControl('', [Validators.required]),
      'orientation': new FormControl('right', [Validators.required]),
      'unit': new FormControl('mm', [Validators.required]),
      'file_quality': new FormControl('normal', [Validators.required]),
      'category': new FormControl('', [Validators.required]),
      'shoetype': new FormControl('a1a1a1', []),
      'size': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'system_size': new FormControl('', [Validators.required]),
      'wide_width_option': new FormControl('', [Validators.required]),
      'front_insock_thickness': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'back_insock_thickness': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'height': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'height_type': new FormControl('', [Validators.required]),
      'ferrato': new FormControl('', [Validators.required, Validators.pattern('[0-9]+'), Validators.min(0)]),
      'ferrato_type': new FormControl('', [Validators.required]),
      'last_bottom_lock_option': new FormControl('', [Validators.required]),
      'toe_shape_type': new FormControl('', [Validators.required]),
    });
  }

  onCreateNew() {
    console.log(this.add_new_form.value)
    if (this.last) {
      this.onUpdate()
    } else {
      if (this.add_new_form.valid) {
        this.SpinnerService.show();
        const formData = new FormData();
        this.stl_last != null ? formData.append('stl_last', this.stl_last, this.stl_last.name) : null;
        const data = this.add_new_form.value;
        Object.keys(data).forEach((key) => {
          if (data[key] != "" && data[key] != null) {
            if(key == 'custom_fields'){
              formData.append(key, JSON.stringify(data[key]));
            }else{
              formData.append(key, data[key]);
            }
          }
        });
        this.subscriptions.push(this.LastService.createNewLast(formData).subscribe({
          next: (response: any) => {
            this.SpinnerService.hide();
            this.MessagesService.set(response.message, "green");
            this.LocalService.selected_last = null
            this.router.navigateByUrl('/last-inventory');
          },
          error: (err) => {
            this.SpinnerService.hide();
          }
        }))
      } else {
        this.MessagesService.set('Fill All Required Fields')
      }
    }
  }

  uploadStl() {
    if (!this.stl_last_scene) {
      this.stllast.nativeElement.click();
    }
  }
  lastChange(_$event: any, type: string) {
    let file = _$event.target.files[0]
    const extension = file.name.split(".").pop();
    if(['stl','obj'].includes(extension)){
        this.stl_last_scene = true
        this.SceneService.show_stl.next({ name: 'stl' ,extension: extension, file: file })
    }else{
      this.MessagesService.set("Only Accept STL and OBJ Files", 'error')
    }
  }

  getLast() {
      this.title = "Update"
      this.LastService.getOneLast(this.last).subscribe({
        next: (response: any) => {
          this.stl_last_scene = true
          this.SceneService.download_stl.next({ ref: response.data.ref, direction: 'stl', isAverage: false, isEdit: true })

          this.right_last_scene = true
          this.SceneService.download_second_stl.next({ ref: response.data.ref, direction: 'right', isAverage: false })
          // this.custom_fields_values = response?.last.custom_fields?.slugs_values
          const numericFields = ['height_value', 'size_value'];
          const radioFields = ['gender', 'category'];
          const formValues: any = {};
          for (const field in this.add_new_form.controls) {
            if (response.data.hasOwnProperty(field)) {
              let value = response.data[field];
              if (radioFields.includes(field)) {
                value = value.toLowerCase();
              }
              if (numericFields.includes(field)) {
                formValues[field] = parseInt(value);
              } else {
                formValues[field] = value;
              }
            }
          }
          this.add_new_form.patchValue(formValues);
          this.dateNow = response.data.invented_at;
        }, error: (error:any) => {
          console.log(error)
          this.MessagesService.set(error, 'error')
        }
      })
  }

  onUpdate() {
    if (this.add_new_form.valid) {
      this.SpinnerService.show();
      const formData = new FormData();
      const data = this.add_new_form.value;
      
      Object.keys(data).forEach((key) => {
        if (data[key] != "" && data[key] != null) {
          if(key == 'custom_fields'){
            formData.append(key, JSON.stringify(data[key]));
          }else{
            formData.append(key, data[key]);
          }
        }
      });
      this.LastService.updateLast(this.last, formData).subscribe({
        next: (response: any) => {
          this.SpinnerService.hide();
          this.MessagesService.set(response.message, "green");
          this.LocalService.selected_last = null
          this.router.navigateByUrl('/last-inventory');
        },
        error: (err:any) => {
          this.SpinnerService.hide();
          console.log(err)
        }
      })
    } else {
      this.MessagesService.set('Fill All Fields')
    }
  }
}
