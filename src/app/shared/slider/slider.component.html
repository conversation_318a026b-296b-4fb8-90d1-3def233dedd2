



  <section class="range-slider container">

   
    <span class="output" [ngClass]="'outputOne-' + mesure" #outputOne></span>
    <span class="output" [ngClass]="'outputTwo-' + mesure" #outputTwo></span>
    <span class="output" [ngClass]="'outputThree-' + mesure" #outputThree></span>
    <span class="output" [ngClass]="'outputFour-' + mesure" #outputFour></span>
    <span class="full-range"></span>

    <span class="incl-01" [ngClass]="mesure + '-01'" #incl01></span>
    <span class="incl-12" [ngClass]="mesure + '-12'" #incl12></span>
    <span class="incl-23" [ngClass]="mesure + '-23'" #incl23></span>
    <span class="incl-34" [ngClass]="mesure + '-34'" #incl34></span>
    <span class="incl-45" [ngClass]="mesure + '-45'" #incl45></span>
    


    <input type="range" [disabled]="!disabled" [min]="min" [max]="max" [value]="value1" step="0.1"
      (input)="mouseUp()" #rangeOne  >
    <input type="range" [disabled]="!disabled" [min]="min" [max]="max" [value]="value2" step="0.1"
      (input)="mouseUp()" #rangeTwo>
    <input type="range" [disabled]="!disabled" [min]="min" [max]="max" [value]="value3" step="0.1"
      (input)="mouseUp()" #rangeThree>
    <input type="range" [disabled]="!disabled" [min]="min" [max]="max" [value]="value4" step="0.1"
      (input)="mouseUp()" #rangeFour>


  </section>

  
