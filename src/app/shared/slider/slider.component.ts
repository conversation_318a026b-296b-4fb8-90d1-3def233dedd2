import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
@Component({
    selector: 'app-slider',
    templateUrl: './slider.component.html',
    styleUrls: ['./slider.component.scss'],
})
export class SliderComponent implements OnInit, AfterViewInit, OnChanges {

    @ViewChild('rangeOne') rangeOne: ElementRef
    @ViewChild('rangeTwo') rangeTwo: ElementRef
    @ViewChild('rangeThree') rangeThree: ElementRef
    @ViewChild('rangeFour') rangeFour: ElementRef
    @ViewChild('incl01') incl01: ElementRef;
    @ViewChild('incl12') incl12: ElementRef;
    @ViewChild('incl23') incl23: ElementRef;
    @ViewChild('incl34') incl34: ElementRef;
    @ViewChild('incl45') incl45: ElementRef;
    @ViewChild('outputOne') outputOne: ElementRef;
    @ViewChild('outputTwo') outputTwo: ElementRef;
    @ViewChild('outputThree') outputThree: ElementRef;
    @ViewChild('outputFour') outputFour: ElementRef;
    

    @Input() disabled: boolean = false
    @Input() mesure = ""
    @Input() min = 0
    @Input() value1 = 0
    @Input() value2 = 0
    @Input() value3 = 0
    @Input() value4 = 0
    @Input() max = 0
    @Output() slide: EventEmitter<any> = new EventEmitter<any>();


    ngOnInit(): void { }

    ngOnChanges(changes: any): void {
        if(this.rangeOne){
            this.mouseUp();
        }
    }

    ngAfterViewInit(): void {
        this.rangeOne.nativeElement.value = this.value1;
        this.rangeTwo.nativeElement.value = this.value2;
        this.rangeThree.nativeElement.value = this.value3;
        this.rangeFour.nativeElement.value = this.value4;
        Promise.resolve().then(() => { this.mouseUp(); })
    }

    mouseUp() {

        let max = this.max - this.min;
        let min = 0;
        if (Number(this.rangeOne.nativeElement.value) > this.value2) {
            this.rangeOne.nativeElement.value = this.value1;
        }
        else {
            this.value1 = Number(this.rangeOne.nativeElement.value);
        }
        if (Number(this.rangeTwo.nativeElement.value) > this.value3 || this.value1 > Number(this.rangeTwo.nativeElement.value)) {
            this.rangeTwo.nativeElement.value = this.value2;
        }
        else {
            this.value2 = Number(this.rangeTwo.nativeElement.value);
        }
        if (Number(this.rangeThree.nativeElement.value) > this.value4 || this.value2 > Number(this.rangeThree.nativeElement.value)) {
            this.rangeThree.nativeElement.value = this.value3;
        }
        else {
            this.value3 = Number(this.rangeThree.nativeElement.value);
        }
        if (this.value3 > Number(this.rangeFour.nativeElement.value)) {
            this.rangeFour.nativeElement.value = this.value4;
        }
        else {
            this.value4 = Number(this.rangeFour.nativeElement.value);
        }
        let valueRangeOne = Number(this.rangeOne.nativeElement.value) - this.min;
        let valueRangeTwo = Number(this.rangeTwo.nativeElement.value) - this.min;
        let valueRangeThree = Number(this.rangeThree.nativeElement.value) - this.min;
        let valueRangeFour = Number(this.rangeFour.nativeElement.value) - this.min;


        if (valueRangeOne > min) {
            this.incl01.nativeElement.width = this.testValue((valueRangeOne) / max * 100 )+ '%';
            this.incl01.nativeElement.style.left = this.testValue((min / max) * 100 )+ '%';
        } 
        else {
            this.incl01.nativeElement.style.width = this.testValue((min - valueRangeOne) / max * 100 )+ '%';
            this.incl01.nativeElement.style.left = this.testValue((valueRangeOne / max) * 100 )+ '%';
        }
        if (valueRangeOne > valueRangeTwo) {
            this.incl12.nativeElement.style.width = this.testValue((valueRangeOne - valueRangeTwo) / max * 100 )+ '%';
            this.incl12.nativeElement.style.left = this.testValue((valueRangeTwo / max) * 100 )+ '%';
        } else {
            this.incl12.nativeElement.style.width = this.testValue((valueRangeTwo - valueRangeOne) / max * 100 )+ '%';
            this.incl12.nativeElement.style.left = this.testValue((valueRangeOne / max) * 100 )+ '%';
        }
        if (valueRangeTwo > valueRangeThree) {
            this.incl23.nativeElement.style.width = this.testValue((valueRangeTwo - valueRangeThree) / max * 100 )+ '%';
            this.incl23.nativeElement.style.left = this.testValue((valueRangeThree / max) * 100 )+ '%';
        } else {
            this.incl23.nativeElement.style.width = this.testValue((valueRangeThree - valueRangeTwo) / max * 100 )+ '%';
            this.incl23.nativeElement.style.left = this.testValue((valueRangeTwo / max) * 100 )+ '%';
        }
        if (valueRangeThree > valueRangeFour) {
            this.incl34.nativeElement.style.width = this.testValue((valueRangeThree - valueRangeFour) / max * 100 )+ '%';
            this.incl34.nativeElement.style.left = this.testValue((valueRangeFour / max) * 100 )+ '%';
        } else {
            this.incl34.nativeElement.style.width = this.testValue((valueRangeFour - valueRangeThree) / max * 100 )+ '%';
            this.incl34.nativeElement.style.left = this.testValue((valueRangeThree / max) * 100 )+ '%';
        }
        if (valueRangeFour > max) {
            this.incl45.nativeElement.style.width = this.testValue((valueRangeFour - max) / max * 100 )+ '%';
            this.incl45.nativeElement.style.left = this.testValue((100 / max) * 100 )+ '%';
        } else {
            this.incl45.nativeElement.style.width = this.testValue((max - valueRangeFour) / max * 100 )+ '%';
            this.incl45.nativeElement.style.left = this.testValue((valueRangeFour / max) * 100 )+ '%';
        }
        this.outputOne.nativeElement.innerHTML = this.rangeOne.nativeElement.value;
        this.outputOne.nativeElement.style.left = this.testValue(valueRangeOne / max * 100 )+ '%';
        this.outputTwo.nativeElement.innerHTML = this.rangeTwo.nativeElement.value;
        this.outputTwo.nativeElement.style.left = this.testValue(valueRangeTwo / max * 100 )+ '%';
        this.outputThree.nativeElement.innerHTML = this.rangeThree.nativeElement.value;
        this.outputThree.nativeElement.style.left = this.testValue(valueRangeThree / max * 100 )+ '%';
        this.outputFour.nativeElement.innerHTML = this.rangeFour.nativeElement.value;
        this.outputFour.nativeElement.style.left = this.testValue(valueRangeFour / max * 100 )+ '%';

        this.slide.emit({ 
            mesure: this.mesure,
            min: this.min,
            value1: this.value1,
            value2: this.value2,
            value3: this.value3,
            value4: this.value4,
            max: this.max })
    }
    testValue(value:number){
        if(value > 100) return 100
        if(value < 0) return 0
        return value
    }


}