import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, Input, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { StaticDataService } from 'src/app/services/static.service';
import { Subscription } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { SearchService } from 'src/app/services/search.service';
import { MessagesService } from '../../messages/messages.service';
import { SpinnerService } from '../../spinner/spinner.service';
// import { search_mesures } from '../../enums/mesures';
import { ActivatedRoute, Router } from '@angular/router';
import { LastMeasuresTranslationKey } from '../../enums/translations';
import { UserService } from 'src/app/services/user.service';

@Component({
  selector: 'app-form-search',
  templateUrl: './form-search.component.html',
  styleUrls: ['./form-search.component.scss'],
  animations: [
    trigger('toggleContent', [
      state('hidden', style({ height: '0', opacity: 0, overflow:'hidden' })),
      state('visible', style({ height: '*', opacity: 1, overflow:'visible'})),
      transition('hidden => visible', animate('300ms ease-in')),
      transition('visible => hidden', animate('300ms ease-out')),
    ]),
    trigger('rotateIcon', [
      state('normal', style({ transform: 'rotate(0deg)' })),
      state('rotated', style({ transform: 'rotate(-90deg)' })),
      transition('normal <=> rotated', animate('300ms ease-in-out')),
    ]),

  ]

})
export class SearchFormComponent implements OnInit, OnDestroy {
  @Input() closable:boolean = true
  isVisible: boolean = false;
  isGeneral: boolean = false;
  isMeasures: boolean = false;
  subscriptions: Subscription[] = []
  search_form!: FormGroup;
  initialValues!: any;
  selected_standard = []
  selected_shoetype = []
  measures :any[]= [];
  lastMeasuresTranslationKey = LastMeasuresTranslationKey;

  genders = signal<string[]>([]);
  categories = signal<string[]>([]);
  lastTypes = signal<string[]>([]);
  archTypes = signal<string[]>([]);
  directions = signal<string[]>([]);

  constructor(public StaticDataService: StaticDataService,
    private SearchService: SearchService,
    private MessagesService: MessagesService,
    private UserService: UserService,
    private SpinnerService: SpinnerService,
    private route: ActivatedRoute,
    private router: Router) {

  }
  ngOnInit(): void {

    this.measures = this.UserService.user.measures
    this.isVisible = false;
    
    // this.subscriptions.push(this.StaticDataService.getStaticData().subscribe({
    //   next: (response: any) => {
    //     this.StaticDataService.setStaticData(response.data)
    //     this.isVisible = false;
    //   },
    //   error: (error) => {
    //     this.isVisible = false;
    //     this.MessagesService.set(error.message, 'error')
    //   }
    // }))
    this.initSearchForm()
    this.subscriptions.push(this.SearchService.search.subscribe(({ filter }) => {
      if(!Object.keys(filter).length) {
        this.resetCheckboxeFilter();
      }
      if(filter.gender) {
        filter.gender.length ? this.genders.set(filter.gender) : this.genders.set([]);
      }

      if(filter.category) {
        filter.category.length ? this.categories.set(filter.category) : this.categories.set([]);
      }

      if(filter.lasttype) {
        filter.lasttype.length ? this.lastTypes.set(filter.lasttype) : this.lastTypes.set([]);
      }

      if(filter.archtype) {
        filter.archtype.length ? this.archTypes.set(filter.archtype) : this.archTypes.set([]);
      }

      if(filter.direction) {
        filter.direction.length ? this.directions.set(filter.direction) : this.directions.set([]);
      }


      this.search_form.reset(this.SearchService.filters)
    }))
  }
  initSearchForm(){
    let measures_group = new FormGroup({})
    this.measures.forEach((mesure:any)=>{
      measures_group.addControl(mesure.slug, 
        new FormGroup({
          'min': new FormControl('', []),
          'max': new FormControl('', []),
        }))
    })
    this.search_form = new FormGroup({
      'reference': new FormControl('', []),
      'standard': new FormControl('', []),
      'gender': new FormControl('', []),
      'category': new FormControl('', []),
      'shoetype': new FormControl('', []),
      'system_size': new FormControl('', []),
      'min_size': new FormControl('', [Validators.pattern('[0-9]+')]),
      'max_size': new FormControl('', [Validators.pattern('[0-9]+')]),
      'wide_width_option': new FormControl('', []),
      'front_insock_thickness_min': new FormControl('', [Validators.pattern('[0-9]+')]),
      'front_insock_thickness_max': new FormControl('', [Validators.pattern('[0-9]+')]),
      'back_insock_thickness_min': new FormControl('', [Validators.pattern('[0-9]+')]),
      'back_insock_thickness_max': new FormControl('', [Validators.pattern('[0-9]+')]),
      'height_type': new FormControl('', []),
      'min_height': new FormControl('', [Validators.pattern('[0-9]+')]),
      'max_height': new FormControl('', [Validators.pattern('[0-9]+')]),
      'ferrato_type': new FormControl('', []),
      'min_ferrato': new FormControl('', [Validators.pattern('[0-9]+')]),
      'max_ferrato': new FormControl('', [Validators.pattern('[0-9]+')]),
      'last_bottom_lock_option': new FormControl('', []),
      'measures_group': measures_group
    });

    const controles = ["reference","standard","gender","category","shoetype","system_size","min_size","max_size","wide_width_option","front_insock_thickness_min","front_insock_thickness_max","back_insock_thickness_min","back_insock_thickness_max","height_type","min_height","max_height","ferrato_type","min_ferrato","max_ferrato","last_bottom_lock_option","measures_group"]
    // Subscribe to value changes

    controles.forEach(controle => {
      this.search_form.get(controle)?.valueChanges.subscribe(value => {
        this.updateQueryParam(controle, value);
      });
    });
    this.initialValues = this.search_form.value;
  }

  updateQueryParam(param: string, value: string) {
  this.router.navigate([], {
    relativeTo: this.route,
    queryParams: { [param]: value },
    queryParamsHandling: 'merge', // keeps existing query params
  });
}
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }
  closeSearch() {
    this.SearchService.open_search.next(false);
  }
  clearSearch() {
    const isDataAnalyticsPage = this.router.url.split('/').at(-1) != 'last-inventory';
    this.SpinnerService.show();
    this.search_form.reset(this.initialValues)
    this.SearchService.filters = {}
    const data = this.search_form.value;
    if (!isDataAnalyticsPage) {
      Object.keys(data).forEach((key) => {
        if (data[key] != "" && data[key] != null) {
          this.SearchService.filters[key] = data[key]
        }
      });
    }
    this.SearchService.isSearch = false
    this.SearchService.search.next({ filter: this.SearchService.filters, page: 0 })
    // this.selected_countries = [];
    // this.selected_scanners = [];
    this.resetCheckboxeFilter();
  }
  select_standard($event: any) {
    this.search_form.controls['standard'].setValue($event);
  }
  select_shoetype($event: any) {
    this.search_form.controls['shoetype'].setValue($event);
  }
  select_wide_width($event: any) {
    this.search_form.controls['wide_width_option'].setValue($event);
  }
  onSubmit() {
    this.SpinnerService.show();
    this.search_form.controls['system_size'].setValue(this.system_size)
    const data = this.search_form.value;
    this.SearchService.filters = {};
    
    Object.keys(data).forEach((key) => {
      if (data[key] != "" && data[key] != null) {
        this.SearchService.filters[key] = data[key]
      }
    });
    let measures_group:any = {}
    if(this.SearchService.filters["measures_group"]){
      measures_group = this.SearchService.filters["measures_group"] ; 
      Object.keys(measures_group).forEach((element : any) => {
        if(!measures_group[element].min && !measures_group[element].max){
          delete measures_group[element]
        }
      });
      this.SearchService.filters["measures_group"] = measures_group;
    }

    this.SearchService.isSearch = true
    this.SearchService.search.next({filter: this.SearchService.filters, page: 0})
  }
  get countries() {
    return []
  }
  get scanners() {
    return []
  }
  get system_size() {
    return this.StaticDataService.system_size + '_size';
  }
  toggleContent(section: number) {
    if (section === 1) {
      this.isGeneral = !this.isGeneral
    }
    if (section === 2) {
      this.isMeasures = !this.isMeasures
    }
  }

  handleSelectedGenders(gender: string) {
    this.updateMultiSelectedFilters(this.genders, gender);
    this.search_form.controls['gender'].setValue(this.genders());
  }

  handleSelectedCategories(category: string) {
    this.updateMultiSelectedFilters(this.categories, category);
    this.search_form.controls['category'].setValue(this.categories());
  }

  handleSelectedLastTypes(lastType: string) {
    this.updateMultiSelectedFilters(this.lastTypes, lastType);
    this.search_form.controls['lasttype'].setValue(this.lastTypes());
  }
  
  handleSelectedArchTypes(archType: string) {
    this.updateMultiSelectedFilters(this.archTypes, archType);
    this.search_form.controls['archtype'].setValue(this.archTypes());
  }

  handleSelectedDirections(direction: string) {
    this.updateMultiSelectedFilters(this.directions, direction);
    this.search_form.controls['direction'].setValue(this.directions());
  }

  updateMultiSelectedFilters(options: any, filter: string) {
    return options.update((prevOptions: any) => 
      prevOptions.includes(filter) ? prevOptions.filter((o: any) => o !== filter) : [...prevOptions, filter]
    );
  }

  resetCheckboxeFilter() {
    this.genders.set([]);
    this.categories.set([]);
    this.lastTypes.set([]);
    this.archTypes.set([]);
    this.directions.set([]);
  }
}