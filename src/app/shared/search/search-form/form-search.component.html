    <form (click)="$event.stopPropagation()" class="search-slider d-flex flex-column h-100"  (ngSubmit)="onSubmit()" [formGroup]="search_form">
        <div class="title h-5 p-3">
            <div>
                <img src="assets/icons/dark-recherche-icon.svg" class="search-icon" alt="Search Icon">
                <h3>{{ 'Search Tool' | translate }}</h3>
            </div>
            <img *ngIf="closable" (click)="closeSearch()" src="assets/icons/cross-icon.svg" alt="close user menu" class="cross-icon">
        </div>
        <div class="w-100 d-flex flex-column p-3 general-section" *ngIf="!isVisible">
            <div class="d-flex align-items-cente" (click)="toggleContent(1); $event.stopPropagation();" style="cursor: pointer;" >
                <img src="assets/icons/open-section-icon.svg" alt="Open Section Icon" 
                [@rotateIcon]="isGeneral ? 'rotated' : 'normal'">
                <h4>{{ 'General' | translate }}</h4>
            </div>
            <section [@toggleContent]="isGeneral ? 'hidden' : 'visible'">
                <app-field name="Reference" width="w-100">
                    <input formControlName="reference" autocomplete="off" class="dm-text-input" type="text"
                        placeholder="Reference" name="reference" id="reference">
                </app-field>
                <!-- <app-field name="BACKEND.Reference" width="w-100">
                    <input formControlName="reference" autocomplete="off" class="dm-text-input" type="text"
                        [placeholder]="'BACKEND.Reference' | translate" name="reference" id="reference">
                </app-field> -->
                <app-searchable-select [placeholder]="'Standard' | translate" name="standard" [list]="StaticDataService.standards" [selected_list]="selected_standard" [isMultiple]="true"
                    (select_event)="select_standard($event)" ></app-searchable-select>
    
                <!-- <app-searchable-select [placeholder]="'SCANNERS.PLACEHOLDER' | translate" name="BACKEND.Scanner" [list]="scanners" [selected_list]="selected_scanners" [isMultiple]="true"
                    (select_event)="select_scanner($event)"></app-searchable-select> -->
                
                <!-- <div class="d-flex align-items-center justify-content-between my-2 p-1">
                    <p class="m-0 w-30">{{ 'BACKEND.Gender' | translate }}</p>
                    <div class="w-70 dm-checkbox-input-v3">

                        <input 
                            value="male" 
                            name="gender" 
                            type="checkbox" 
                            id="gender_male"
                            [checked]="genders().includes('male')"
                            (click)="handleSelectedGenders('male')"
                        >
                        <label for="gender_male">{{ 'BACKEND.Male' | translate }}</label>
        
                    </div>
                </div> -->
                <app-field name="Gender">
                    <div class="dm-radio-input-v1 w-100 ps-4">
                        <ng-container *ngFor="let gender of StaticDataService.genders">
                            <input autocomplete="off" formControlName="gender" type="radio" [value]="gender.slug"
                                name="gender" id="{{ 'gender_' + gender.slug}}" >
                            <label for="{{ 'gender_' + gender.slug}}">{{gender.name | translate }}</label>
                        </ng-container>
                    </div>
                </app-field>



                <!-- <div class="d-flex align-items-center justify-content-between my-2 p-1">
                    <p class="m-0 w-30">{{ 'Category' | translate }}</p>
                    <div class="w-70 dm-checkbox-input-v3">
                        <input 
                            value="adult" 
                            name="category" 
                            type="checkbox" 
                            id="category_adult"
                            [checked]="categories().includes('adult')"
                            (click)="handleSelectedCategories('adult')"
                        >
                        <label for="category_adult">{{ 'Adult' | translate }}</label>
        
                        <input 
                            value="kid" 
                            name="category" 
                            type="checkbox" 
                            id="category_kid"
                            [checked]="categories().includes('kid')"
                            (click)="handleSelectedCategories('kid')"
                        >
                        <label for="category_kid">{{ 'Kid' | translate }}</label>
                        
                    </div>
                </div> -->


                <app-field name="Category">
                    <div class="dm-radio-input-v1 w-100 ps-4">
                        <ng-container *ngFor="let category of StaticDataService.categories">
                            <input autocomplete="off" formControlName="category" type="radio" [value]="category.slug"
                                name="category" id="{{ 'category_' + category.slug}}" >
                            <label for="{{ 'category_' + category.slug}}">{{category.name | translate }}</label>
                        </ng-container>
                    </div>
                </app-field>


                <app-searchable-select [placeholder]="'Shoetype' | translate" name="shoetype" [list]="StaticDataService.shoetypes" [selected_list]="selected_shoetype" [isMultiple]="true"
                    (select_event)="select_shoetype($event)" ></app-searchable-select>


                <div class="d-flex align-items-start justify-content-between flex-wrap my-2 p-1">
                
                    <p class="m-0 w-30">{{ 'Size' | translate }}</p>
    
                    <div class="w-70 dm-radio-input-v4 justify-content-end">
                        <ng-container *ngFor="let system_size of StaticDataService.system_sizes">
                            <input formControlName="system_size" name="system_size" [value]="system_size.slug" type="radio" id="{{ system_size.slug }}" >
                            <label style="min-width: 2.5rem;" for="{{ system_size.slug }}">{{ system_size.name }}</label>
                        </ng-container>
                    </div>
    
                    <div class="w-100 min_max">
                        <label class="p-1 px-2" for="min_size">{{ 'Minimum' | translate }}</label>
                        <input formControlName="min_size" name="min_size" min="0" max="400" type="number" id="min_size">
    
                        <label class="p-1 px-2" for="max_size">{{ 'Maximum' | translate }}</label>
                        <input formControlName="max_size" name="max_size" min="0" max="400" type="number" id="max_size">
                    </div>
    
                </div>


                <app-searchable-select *ngIf="search_form.value.system_size"
            [placeholder]="search_form.value.wide_width_option ? search_form.value.wide_width_option :  'Select Wide Width'" name="Wide Width" slug="value"
            [list]="StaticDataService.wide_width_options[search_form.value.system_size] ?? []"
                (select_event)="select_wide_width($event)"></app-searchable-select>


                <div class="d-flex align-items-center justify-content-between flex-wrap my-3 p-1">
                    
                    <p class="m-0 w-30">{{ 'Front Insock Thickness' | translate }}</p>
    
                    <div class="w-70 min_max">
                        <label class="p-1 px-2" for="front_insock_thickness_min">{{ 'Minimum' | translate }}</label>
                        <input formControlName="front_insock_thickness_min"  name="front_insock_thickness_min" min="0" max="300" type="number" id="front_insock_thickness_min">
    
                        <label class="p-1 px-2" for="front_insock_thickness_max">{{ 'Maximum' | translate }}</label>
                        <input formControlName="front_insock_thickness_max" name="front_insock_thickness_max" min="0" max="300" type="number" id="front_insock_thickness_max">
                    </div>
    
                </div>

                <div class="d-flex align-items-center justify-content-between flex-wrap my-3 p-1">
                    
                    <p class="m-0 w-30">{{ 'Back Insock Thickness' | translate }}</p>
    
                    <div class="w-70 min_max">
                        <label class="p-1 px-2" for="back_insock_thickness_min">{{ 'Minimum' | translate }}</label>
                        <input formControlName="back_insock_thickness_min"  name="back_insock_thickness_min" min="0" max="300" type="number" id="back_insock_thickness_min">
    
                        <label class="p-1 px-2" for="back_insock_thickness_max">{{ 'Maximum' | translate }}</label>
                        <input formControlName="back_insock_thickness_max" name="back_insock_thickness_max" min="0" max="300" type="number" id="back_insock_thickness_max">
                    </div>
    
                </div>

                <div class="d-flex align-items-start justify-content-between flex-wrap my-2 p-1">
                
                    <p class="m-0 w-30">{{ 'Height' | translate }}</p>
    
                    <div class="w-50 dm-radio-input-v4 justify-content-center">
                        <ng-container *ngFor="let height_type of StaticDataService.height_types">
                            <input formControlName="height_type" name="height_type" [value]="height_type.slug" type="radio" id="{{ height_type.slug }}" >
                            <label style="min-width: 4rem;" for="{{ height_type.slug }}">{{ height_type.name }}</label>
                        </ng-container>
                    </div>
    
                    <div class="w-100 min_max">
                        <label class="p-1 px-2" for="min_height">{{ 'Minimum' | translate }}</label>
                        <input formControlName="min_height" name="min_height" min="0" max="400" type="number" id="min_height">
    
                        <label class="p-1 px-2" for="max_height">{{ 'Maximum' | translate }}</label>
                        <input formControlName="max_height" name="max_height" min="0" max="400" type="number" id="max_height">
                    </div>
    
                </div>
                <div class="d-flex align-items-start justify-content-between flex-wrap my-2 p-1">
                
                    <p class="m-0 w-30">{{ 'Ferrato' | translate }}</p>
    
                    <div class="w-70 dm-radio-input-v4 justify-content-center">
                        <ng-container *ngFor="let ferrato_type of StaticDataService.ferrato_types">
                            <input formControlName="ferrato_type" name="ferrato_type" [value]="ferrato_type.slug" type="radio" id="{{ ferrato_type.slug }}" >
                            <label style="min-width: 4rem;" for="{{ ferrato_type.slug }}">{{ ferrato_type.name }}</label>
                        </ng-container>
                    </div>
    
                    <div class="w-100 min_max">
                        <label class="p-1 px-2" for="min_ferrato">{{ 'Minimum' | translate }}</label>
                        <input formControlName="min_ferrato" name="min_ferrato" min="0" max="400" type="number" id="min_ferrato">
    
                        <label class="p-1 px-2" for="max_ferrato">{{ 'Maximum' | translate }}</label>
                        <input formControlName="max_ferrato" name="max_ferrato" min="0" max="400" type="number" id="max_ferrato">
                    </div>
    
                </div>

                <app-field name="Last Bottom Lock">
                    <div class="dm-radio-input-v1 w-100 ps-4">
                        <ng-container *ngFor="let last_bottom_lock_option of StaticDataService.last_bottom_lock_options">
                            <input autocomplete="off" formControlName="last_bottom_lock_option" type="radio" [value]="last_bottom_lock_option.slug"
                                name="last_bottom_lock_option" id="{{ 'last_bottom_lock_option_' + last_bottom_lock_option.slug}}" >
                            <label for="{{ 'last_bottom_lock_option_' + last_bottom_lock_option.slug}}">{{last_bottom_lock_option.name | translate }}</label>
                        </ng-container>
                    </div>
                </app-field>

                

    <!-- 
                <div class="d-flex align-items-center justify-content-between my-2 p-1">
                    <p class="m-0 w-30">{{ 'BACKEND.Category' | translate }}</p>
                    <div class="w-70 dm-checkbox-input-v3">
                        <input 
                            value="adult" 
                            name="category" 
                            type="checkbox" 
                            id="category_adult"
                            [checked]="categories().includes('adult')"
                            (click)="handleSelectedCategories('adult')"
                        >
                        <label for="category_adult">{{ 'BACKEND.Adult' | translate }}</label>
        
                        <input 
                            value="kid" 
                            name="category" 
                            type="checkbox" 
                            id="category_kid"
                            [checked]="categories().includes('kid')"
                            (click)="handleSelectedCategories('kid')"
                        >
                        <label for="category_kid">{{ 'BACKEND.Kid' | translate }}</label>
                        <input 
                            value="unknown" 
                            name="category" 
                            type="checkbox" 
                            id="category_unknown"
                            [checked]="categories().includes('unknown')"
                            (click)="handleSelectedCategories('unknown')"
                        >
                        <label for="category_unknown">{{ 'BACKEND.Unknown' | translate }}</label>
                    </div>
                </div>
    
    
                
                <div class="d-flex align-items-start justify-content-between flex-wrap my-2 p-1">
                    
                    <p class="m-0 w-30">{{ 'BACKEND.Size' | translate }}</p>
    
                    <div class="w-70 dm-radio-input-v4 justify-content-end">
                        <input formControlName="system_size" value="eu_size" name="system_size" type="radio" id="size_eu" checked="checked">
                        <label class="p-1 px-2" for="size_eu">EU</label>
                        
                        <input formControlName="system_size" value="uk_size" name="system_size" type="radio" id="size_uk">
                        <label class="p-1 px-2" for="size_uk">UK</label>
    
                        <input formControlName="system_size" value="us_size" name="system_size" type="radio" id="size_us">
                        <label class="p-1 px-2" for="size_us">US</label>
                    </div>
    
                    <div class="w-100 min_max">
                        <label class="p-1 px-2" for="min_size">{{ 'COMMON.MINIMUM' | translate }}</label>
                        <input formControlName="min_size" name="min_size" min="0" max="400" type="number" id="min_size">
    
                        <label class="p-1 px-2" for="max_size">{{ 'COMMON.MAXIMUM' | translate }}</label>
                        <input formControlName="max_size" name="max_size" min="0" max="400" type="number" id="max_size">
                    </div>
    
                </div>
    
                <div class="d-flex align-items-start justify-content-between flex-wrap my-2 p-1">
                    
                    <p class="m-0 w-30">{{ 'BACKEND.Height' | translate }}</p>
    
                    <div class="w-70 dm-radio-input-v4 justify-content-end">
                        <input value="cm" name="height" type="radio" id="height_cm" checked="checked">
                        <label class="p-1 px-2" for="height_cm">cm</label>
                    </div>
    
                    <div class="w-100 min_max">
                        <label class="p-1 px-2" for="min_height">{{ 'COMMON.MINIMUM' | translate }}</label>
                        <input formControlName="min_height"  name="min_height" min="0" max="300" type="number" id="min_height">
    
                        <label class="p-1 px-2" for="max_height">{{ 'COMMON.MAXIMUM' | translate }}</label>
                        <input formControlName="max_height" name="max_height" min="0" max="300" type="number" id="max_height">
                    </div>
    
                </div>
    
                <div class="d-flex align-items-start justify-content-between flex-wrap my-2 p-1">
                    
                    <p class="m-0 w-30">{{ 'BACKEND.Weight' | translate }}</p>
    
                    <div class="w-70 dm-radio-input-v4 justify-content-end">
                        <input value="kg" name="weight" type="radio" id="weight_kg" checked="checked">
                        <label class="p-1 px-2" for="weight_kg">kg</label>
                    </div>
    
                    <div class="w-100 min_max">
                        <label class="p-1 px-2" for="min_weight">{{ 'COMMON.MINIMUM' | translate }}</label>
                        <input formControlName="min_weight" name="min_weight" min="0" max="400" type="number" id="min_weight">
    
                        <label class="p-1 px-2" for="max_weight">{{ 'COMMON.MAXIMUM' | translate }}</label>
                        <input formControlName="max_weight" name="max_weight" min="0" max="400" type="number" id="max_weight">
                    </div>
    
                </div>
    
                <div class="d-flex align-items-start justify-content-between flex-wrap my-2 p-1">
                    
                    <p class="m-0 w-30">{{ 'BACKEND.Age' | translate }}</p>

    
                    <div class="w-100 min_max">
                        <label class="p-1 px-2" for="min_age">{{ 'COMMON.MINIMUM' | translate }}</label>
                        <input formControlName="min_age"  name="min_age" min="0" max="150" type="number" id="min_age">
    
                        <label class="p-1 px-2" for="max_age">{{ 'COMMON.MAXIMUM' | translate }}</label>
                        <input formControlName="max_age" name="max_age" min="0" max="150" type="number" id="max_age">
                    </div>
    
                </div>
    
    
    
                <div class="d-flex align-items-start justify-content-between my-2 p-1">
                    <p class="m-0 w-30">{{ 'BACKEND.Last Type' | translate }}</p>
                    <div class="w-70 dm-checkbox-input-v1 justify-content-end">
                        <input 
                            value="greek" 
                            name="lasttype" 
                            type="checkbox" 
                            id="lasttype_greek"
                            [checked]="lastTypes().includes('greek')"
                            (click)="handleSelectedLastTypes('greek')"
                        >
                        <label class="w-40 justify-content-end p-1 px-2" for="lasttype_greek">{{ 'BACKEND.Greek' | translate }}</label>
        
                        <input 
                            value="egyptian" 
                            name="lasttype" 
                            type="checkbox" 
                            id="lasttype_egyptian"
                            [checked]="lastTypes().includes('egyptian')"
                            (click)="handleSelectedLastTypes('egyptian')"
                        >
                        <label class="w-40 justify-content-end p-1 px-2" for="lasttype_egyptian">{{ 'BACKEND.Egyptian' | translate }}</label>
        
                        <input 
                            value="roman" 
                            name="lasttype" 
                            type="checkbox" 
                            id="lasttype_roman"
                            [checked]="lastTypes().includes('roman')"
                            (click)="handleSelectedLastTypes('roman')"
                        >
                        <label class="w-40 justify-content-end p-1 px-2" for="lasttype_roman">{{ 'BACKEND.Roman' | translate }}</label>
            
                        <input 
                            value="german" 
                            name="lasttype" 
                            type="checkbox" 
                            id="lasttype_german"
                            [checked]="lastTypes().includes('german')"
                            (click)="handleSelectedLastTypes('german')"
                        >
                        <label class="w-40 justify-content-end p-1 px-2" for="lasttype_german">{{ 'BACKEND.German' | translate }}</label>
    
                        <input 
                            value="celtic" 
                            name="lasttype" 
                            type="checkbox" 
                            id="lasttype_celtic"
                            [checked]="lastTypes().includes('celtic')"
                            (click)="handleSelectedLastTypes('celtic')"
                        >
                        <label class="w-40 justify-content-end p-1 px-2" for="lasttype_celtic">{{ 'BACKEND.Celtic' | translate }}</label>
                    </div>
                </div>
    
                <div class="d-flex align-items-center justify-content-between my-2 p-1">
                    <p class="m-0 w-30">{{ 'BACKEND.Arch Type' | translate }}</p>
                    <div class="w-70 dm-checkbox-input-v3">
                        <input
                            value="flat" 
                            name="archtype" 
                            type="checkbox" 
                            id="archtype_flat"
                            [checked]="archTypes().includes('flat')"
                            (click)="handleSelectedArchTypes('flat')"
                        >
                        <label for="archtype_flat">{{ 'BACKEND.Flat' | translate }}</label>
        
                        <input 
                            value="normal" 
                            name="archtype" 
                            type="checkbox" 
                            id="archtype_normal"
                            [checked]="archTypes().includes('normal')"
                            (click)="handleSelectedArchTypes('normal')"
                        >
                        <label for="archtype_normal">{{ 'BACKEND.Normal' | translate }}</label>
        
                        <input 
                            value="high" 
                            name="archtype" 
                            type="checkbox" 
                            id="archtype_high"
                            [checked]="archTypes().includes('high')"
                            (click)="handleSelectedArchTypes('high')"
                        >
                        <label for="archtype_high">{{ 'BACKEND.High' | translate }}</label>
                    </div>
                </div> -->
    
                <!-- <div class="d-flex align-items-center justify-content-between my-2 p-1">
                    <p class="m-0 w-30">{{ 'BACKEND.L / R' | translate }}</p>
                    <div class="w-70 dm-checkbox-input-v3">
                        <input
                            value="right" 
                            name="direction" 
                            type="checkbox" 
                            id="direction_right"
                            [checked]="directions().includes('right')"
                            (click)="handleSelectedDirections('right')"
                        >
                        <label for="direction_right">{{ 'FOOT_VIEW.RIGHT' | translate }}</label>
        
                        <input
                            value="left" 
                            name="direction" 
                            type="checkbox" 
                            id="direction_left"
                            [checked]="directions().includes('left')"
                            (click)="handleSelectedDirections('left')"
                        >
                        <label for="direction_left">{{ 'FOOT_VIEW.LEFT' | translate }}</label>
                    </div>
                </div> -->
            </section>
            <div class="d-flex align-items-cente" (click)="toggleContent(2); $event.stopPropagation();" style="cursor: pointer;" >
                <img src="assets/icons/open-section-icon.svg" alt="Open Section Icon" 
                [@rotateIcon]="isMeasures ? 'rotated' : 'normal'">
                <h4>{{ 'Measures' | translate }}</h4>
            </div>
            <section [@toggleContent]="isMeasures ? 'hidden' : 'visible'" formGroupName="measures_group" class="measures_group">
                <div *ngFor="let measure of measures" 
                    class="d-flex align-items-start justify-content-between flex-wrap my-2 p-1" 
                    formGroupName="{{measure.slug}}">
                    
                    <p class="m-0 w-30">{{ measure.name | translate}}</p>
    
                    <div class="min_max">
                        <label class="p-1 px-2" for="measures_group_min_{{measure.slug}}">{{ 'Minimum' | translate }}</label>
                        <input formControlName="min"  name="min" min="0" max="150" type="number" id="measures_group_min_{{measure.slug}}">
    
                        <label class="p-1 px-2" for="measures_group_max_{{measure.slug}}">{{ 'Maximum' | translate }}</label>
                        <input formControlName="max" name="max" min="0" max="150" type="number" id="measures_group_max_{{measure.slug}}">
                    </div>
                </div>
            </section>

        </div>
        <div class="w-100 d-flex justify-content-around align-items-center pt-4 pb-2 buttons">
            <button class="btn btn-primary px-5 w-40" (click)="clearSearch()" type="button" *ngIf="!closable">{{ 'Clear' | translate }}</button>
            <button class="btn btn-primary px-5 w-40" (click)="closeSearch()" type="button" *ngIf="closable">{{ 'Close' | translate }}</button>
            <button class="btn btn-primary px-5 w-40" type="submit">{{ 'Search' | translate }}</button>
        </div>
        <!-- <app-spinner-html *ngIf="isVisible"></app-spinner-html> -->
    </form>