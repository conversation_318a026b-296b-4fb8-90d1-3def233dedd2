<div class="back">
    <div class="full-screen-card">
            <h1 class="title">Full Screen View</h1>
            <img src="assets/icons/cross-icon.svg" alt="close icon" class="close-icon" (click)="close()">
            <div class="px-2 pb-2 metrics-table" *ngIf="isProcessed()">
                <h4 class="w-100" (click)="toggleContent();$event.stopPropagation();">
        
                    <div class="all-metrics-icon">
                        <img width="16px"
                            src="assets/icons/{{ showAll ? 'eye-icon-white.svg': 'close-eye-icon-white.svg' }}"
                            alt="eye icon" (click)="toggleAll(); $event.stopPropagation();">
                        <span style=" font-size:1rem;">{{ 'All' | translate }}</span>
                    </div>
                    {{ 'Metrics' | translate }}
                    <img 
                        src="assets/icons/down-white-icon.svg" alt="down-white-icon" class="hide-icon"
                        [@rotateIcon]="isMetrics ? 'rotated' : 'normal'">
                </h4>
                <div class="w-100 flex-grow-1" [@toggleContent]="isMetrics ? 'hidden' : 'visible'">
                    <table class="w-100">
                        <tbody class="w-100 all-measures">
                            <ng-container *ngFor="let mesure of groups(); let i = index">
                                <tr class="w-50 test">
                                    <td class="w-100 bordered">
                                        <span class="w-15"><img *ngIf="mesure.mesure" (click)="toggle(mesure.slug)"
                                                src="assets/icons/{{ mesure.show ? '':'close-' }}eye-icon.svg"
                                                alt="eye icon"></span>
                                        <span 
                                            [ngClass]="{'w-55': !mesure.label, 'group-label': mesure.label}">
                                            {{ (mesure.label ? mesure.label : mesure.name ) | translate }}
                                        </span>
                                        <span class="w-30" *ngIf="mesure.data | isNumber">{{ mesure.data }}</span>
                                        <span 
                                            class="w-30" 
                                            *ngIf="!(mesure.data | isNumber)">{{ mesure.data | capitalize | translate }}
                                        </span>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
            </div>
            <app-spinner-html *ngIf="show_spinner" class="spinner"></app-spinner-html>
            <app-main-scene [zoom]="3"></app-main-scene>
    </div>
</div>
