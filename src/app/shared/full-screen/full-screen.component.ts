import { AfterViewInit, Component, Input, OnChanges, OnDestroy, OnInit, signal } from '@angular/core';
import { Subscription } from 'rxjs';
import { SceneService } from 'src/app/scene/main/scene.service';
import { LocalService } from 'src/app/services/local.service';
import { SpinnerService } from '../spinner/spinner.service';
import { CurvesService } from 'src/app/services/curves.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { LastInfosTranslationKey, LastMeasuresTranslationKey } from '../enums/translations';

@Component({
  selector: 'app-full-screen',
  templateUrl: './full-screen.component.html',
  styleUrls: ['./full-screen.component.scss'],
  animations: [
    trigger('toggleContent', [
      state('hidden', style({ opacity: '0', height: '0', overflow:'hidden' })),
      state('visible', style({ opacity: '1', overflow:'visible'})),
      transition('hidden => visible', animate('300ms ease-in')),
      transition('visible => hidden', animate('300ms ease-out')),
    ]),
    trigger('rotateIcon', [
      state('normal', style({ transform: 'rotate(0deg)' })),
      state('rotated', style({ transform: 'rotate(90deg)' })),
      transition('normal <=> rotated', animate('300ms ease-in-out')),
    ]),
  ]
})
export class FullScreenComponent implements OnInit, OnDestroy, OnChanges {
  subscriptions: Subscription[] = [];
  @Input() selected_last: any = null
  @Input() showAll:boolean = false
  mesures: any[] = [];
  show_spinner: boolean = true
  isMetrics: boolean = false;
  // Signals
  groups = signal<any[]>([]);
  // Enums
  lastInfosTranslations = LastInfosTranslationKey;
  lastMeasuresTranslationKey = LastMeasuresTranslationKey;

  constructor(
    private localService: LocalService,
    private sceneService: SceneService,
    private spinnerService: SpinnerService,
    private curvesService: CurvesService
  ) { }


  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe();
    }
  }
  ngOnChanges(changes: any): void {
      if (changes.selected_last) {
        this.getObjectToScene();
      }
  }
  ngOnInit(): void {
    this.subscriptions.push(this.spinnerService.spinnerSubject.subscribe((data: any) => {
      if (data.spinner_number == 1) {
        this.show_spinner = data.status;
      }
    }))
    this.subscriptions.push(this.localService.selected_last_data_arrived.subscribe((data) => {
      this.mesures = data.mesures
      console.log(data)
      this.mesures = Object.values(data.mesures).filter((el:any)=>el.type != 'group');
      const groups: any[] = [];
      const types: string[] = [];

      this.mesures.forEach(({ type, name }) => {
          if (type === 'Line') {
              if (name.includes('Height') && types.indexOf('Height') == -1) {
                  types.push('Height');
              }
              if (name.includes('Width') && types.indexOf('Width') == -1) {
                  types.push('Width');
              }
          } else {
              if (types.indexOf(type) == -1) {
                  types.push(type);
              }
          }
      });

      const measuresWithExactTypes = this.mesures.map(
          m => m.type == 'Line' ? { ...m, type: m.name.includes('Height') ? 'Height' : 'Width' } : m
      );

      types.map(type => {
          groups.push({label: this.curvesService.getMeasuresGroupKey(type)});
          measuresWithExactTypes.forEach(group => {
              if (group.type === type) {
                  groups.push(group);
              }
          });
      });
      this.groups.set(groups);
      console.log(groups)
    }))
    this.subscriptions.push(this.curvesService.curve_status.subscribe(({ slug, status}) => {
        let index = this.mesures.findIndex((mesure) => mesure.slug == slug)
        if (index != -1) {
          this.mesures[index].show = status       
        }
        this.groups.update(prevGroups => {
          return prevGroups.map(g => g.slug == slug ? { ...g, show: status } : { ...g })
        });
    }))

  }
  toggle(slug: string) {
    this.curvesService.toggleMesure.next({ slug: slug });
  }
  toggleAll() {
    const status = this.showAll;
    this.groups().forEach((mesure: any) => {
      if (mesure.show == status) {
          this.toggle(mesure.slug);
      }
    });
  }
  getObjectToScene() {
    if (this.isProcessed()) {
      this.spinnerService.show(1);
      this.sceneService.download_stl.next({ ref: this.selected_last.ref, id: this.selected_last.id })
    } else {
      this.sceneService.clear_scene.next();
      this.spinnerService.hide(1);
    }

  }
  isProcessed() {
    return this.selected_last.status == 'processed'
  }
  toggleContent() {
    this.isMetrics = !this.isMetrics
  }
  close() {
    this.localService.open_fullscreen.next(false)
    this.getObjectToScene()
  }
}
