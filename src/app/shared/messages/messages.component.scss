@import "../../../styles/variables";
.messages{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba($dark, 0.2);
    z-index: 200;
    &>div{

        &.error{
            background-color: lighten($danger, 30%);
            h5{
                color: $danger;
            }
        }
        &.green{
            background-color: lighten($success, 65%);
            h5{
                color: $success;
            }
        }        
        &.normal{
            background-color: white;
            h5{
                color: $primary;
            }
        }

        width:30%;
        max-width: 500px;
        max-height: 250px;
        border-radius: 1rem;
        button{
            color: white;
            background-color: $primary;
        }
        .header{
            position: relative;
            padding: 0.5rem;
            h5{
                font-size: 1.5rem;
                font-weight: 700;
            }
            .cross-icon{
                position: absolute;
                width: 1.7rem;
                right: 0.5rem;
                top: 0.5rem;
                padding: 0.3rem;
                border-radius: 1rem;
                background-color: rgba($dark, 0.2);
                cursor: pointer;
                transition: all 150ms ease-in;
                &:hover{
                    width: 1.9rem;
                    right: 0.4rem;
                    top: 0.4rem;
                    background-color: rgba($danger, 0.5);
                }
            }
        }
        .body{
            padding: 0.5rem;
            text-align: center;
        }
        .laster{
            padding: 0.5rem;
        }
    }
}