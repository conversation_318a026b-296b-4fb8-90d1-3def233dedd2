@import "../../../styles/variables";

.app-searchable-select {

    display: flex;
    align-items: center;
    width: 100%;
    border-radius: 5px;
    border-bottom-right-radius: 0;
    position: relative;
    margin: 0.75vh 0;
    border: 1px solid $secondary;
    padding: 0.5rem;
    position: relative;

    &>.label {
        display: flex;
        align-items: center;
        border-right: 1px solid $secondary;
        padding-right: 1rem;

        p,
        span {
            margin: 0 0.5rem;
            text-transform: capitalize;
            font-size: 1rem;
        }

        &>span {
            position: relative;
            display: block;
            width: 1rem;
            height: 1rem;
            text-align: center;
            border-radius: 1rem;
            color: #fff;
            margin: 0.1rem 0.3rem;
            cursor: pointer;
            line-height: 1.4rem;

            &::before {
                display: none;
                position: absolute;
                content: "";
                top: 50%;
                left: calc(100% + 4px);
                background-color: $dark;
                color: white;
                width: 1rem;
                height: 1rem;
                transform: rotate(45deg) translateX(-60%);
                z-index: 3;
            }

            &::after {
                font-size: 0.8rem;
                z-index: 3;
                display: none;
                position: absolute;
                content: attr(data-help);
                top: 50%;
                transform: translateY(-50%);
                left: 100%;
                background-color: $dark;
                padding: 5px;
                border-radius: 5px;
                color: white;
                width: max-content;
                max-width: 200px;
            }

            &:hover::after,
            &:hover::before {
                display: block;
            }

            &.help {
                background-color: rgba($dark, 0.3);
                font-size: 0.8rem;
            }

            &.required {
                background-color: $danger;
            }
        }
    }

    &>.input {
        cursor: pointer;
        display: flex;
        align-items: center;
        flex: 1 1 auto;
        padding: 0 0.5rem;
        position: relative;

        input {
            border: 0;
            outline: 0;
            padding: 0 1rem;
            font-size: 1.2rem;
            height: 1.5rem;
            flex: 1 1 auto;
            cursor: pointer;
            text-transform: capitalize;
        }

        .list-container {
            width: calc(100% + 0.5rem) !important;
            max-height: 150px;
            overflow-y: scroll;
            overflow-x: hidden;
            position: absolute;
            top: calc(100% + 0.5rem + 2px);
            left: 0;
            z-index: 5;

            ul {
                margin: 0;
                padding: 0;
                z-index: 10;
                background-color: white;
                border: 1px solid $secondary;
                overflow-y: auto;
                transition: height 1s ease-in;
                width: calc(100% + 1px);


                li {
                    list-style: none;
                    font-size: 1rem;
                    cursor: pointer;
                    margin-top: 0.25rem;
                    background-color: rgba($color: $secondary, $alpha: 50%);
                    border-radius: 0.1rem;
                    padding: 0.2rem;

                    .selected_icon {
                        display: block;
                        position: absolute;
                        right: 0.25rem;
                        top: calc(50% - 0.5rem);
                        width: 1.1rem;
                        height: 1.1rem;
                        z-index: 22;
                    }
                }
            }

        }
    }

    .icon {
        cursor: pointer;
        width: 0.9rem;
        position: absolute;
        right: 0.5rem;
        top: 40%;
        transform: rotate(0deg);
        transition: transform 200ms ease-in;

        &.open {
            transform: rotate(180deg);
        }
    }
    .disabled{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba($dark, 0.3);
        z-index: 1;
        cursor: not-allowed;
    }
}

ul.selected-filter {
    padding: 0.2rem 0.5rem;
    margin: 0;
    overflow-y: auto;
    &>li{
        padding: 0.1rem 0.5rem;
        color: white;
        background-color: rgba($dark, 0.7);
        border-radius: 1rem;
        margin: 0 0.5rem;
        list-style: none;
        font-size: 0.8rem;
        font-weight: 100; 
        min-width: fit-content;       
    }
}