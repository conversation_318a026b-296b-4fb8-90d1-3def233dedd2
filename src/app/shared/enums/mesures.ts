export const mesures = [
  { slug: "lastType", name: "Last Type"},
  { slug: "lastLength",name: "Last Length"},
  { slug: "size",name: "<PERSON><PERSON>"},
  { slug: "navicularPerimeter",name: "Navicular Perimeter"},
  { slug: "entryPerimeter",name: "Entry Perimeter"},
  { slug: "jointPerimeter",name: "Joint Perimeter"},
  { slug: "toePerimeter",name: "Toe Perimeter"},
  { slug: "heelWidth",name: "Heel Width"},
  { slug: "heelWidthD",name: "Heel Width DL"},
  { slug: "jointWidth",name: "<PERSON><PERSON>idth"},
  { slug: "jointWidthD",name: "<PERSON><PERSON>idth <PERSON>"},
  { slug: "instep1Height",name: "Instep 1 Height"},
  { slug: "instep2Height",name: "Instep 2 Height"},
  { slug: "metaHeight",name: "Meta Height"},
  { slug: "archHeight",name: "Arch Height"},
  { slug: "heelHeight",name: "Heel Height"},

  { slug: "metaExtPoint",name: "Meta Ext Point"},
  { slug: "metaIntPoint",name: "Meta Int Point"},
  { slug: "heelExtPoint",name: "Heel Ext Point"},
  { slug: "heelIntPoint",name: "Heel Int Point"},
  { slug: "morphoGroundPoint",name: "Morpho Ground Point"},
  { slug: "toeHeight", name: "Toe Height"},
  { slug: "halluxValgusAngle", name: "Hallux Valgus Angle"}
];

export const search_mesures =[
  { slug:"lastLength", name: "Last Length"},
  { slug:"navicularPerimeter", name: "Navicular Perimeter"},
  { slug:"entryPerimeter", name: "Entry Perimeter"},
  { slug:"jointPerimeter", name: "Joint Perimeter"},
  { slug:"toePerimeter", name: "Toe Perimeter"},
  { slug:"heelWidth", name: "Heel Width"},
  { slug:"heelWidthD", name: "Heel Width DL"},
  { slug:"jointWidth", name: "Join Width"},
  { slug:"jointWidthD", name: "Join Width DL"},
  { slug:"instep1Height", name: "Instep 1 Height"},
  { slug:"instep2Height", name: "Instep 2 Height"},
  { slug:"metaHeight", name: "Meta Height"},
  { slug:"archHeight", name: "Arch Height"},
  { slug:"toeHeight", name: "Toe Height"},
  { slug:"metaExtPoint", name: "Meta Ext Point"},
  { slug:"metaIntPoint", name: "Meta Int Point"},
  { slug:"heelExtPoint", name: "Heel Ext Point"},
  { slug:"heelIntPoint", name: "Heel Int Point"},
  { slug:"morphoGroundPoint", name: "Morpho Ground Point"},
  { slug:"halluxValgusAngle", name: "Hallux Valgus Angle"},
  { slug:"lastPrint", name: "Last Print"},
  { slug:"arch", name: "Arch"},
  ]
export const search_mesures_object:any ={
  lastLength:"Last Length",
  navicularPerimeter:"Navicular Perimeter",
  entryPerimeter:"Entry Perimeter",
  jointPerimeter:"Joint Perimeter",
  toePerimeter:"Toe Perimeter",
  heelWidth:" Heel Width",
  heelWidthD:"Heel Width DL",
  jointWidth:"Join Width",
  jointWidthD:"Join Width DL",
  instep1Height:"Instep 1 Height",
  instep2Height:"Instep 2 Height",
  metaHeight:"Meta Height",
  archHeight:"Arch Height",
  toeHeight:"Toe Height",
  metaExtPoint:"Meta Ext Point",
  metaIntPoint:"Meta Int Point",
  heelExtPoint:"Heel Ext Point",
  heelIntPoint:"Heel Int Point",
  morphoGroundPoint:"Morpho Ground Point",
  halluxValgusAngle:"Hallux Valgus Angle",
  lastPrint:"Last Print",
  arch:"Arch",
}

// export const groupes_mesures:any ={
//   lastLength:"Last Length",
//   navicularPerimeter:"Navicular Perimeter",
//   entryPerimeter:"Entry Perimeter",
//   jointPerimeter:"Joint Perimeter",
//   toePerimeter:"Toe Perimeter",
//   heelWidth:" Heel Width",
//   heelWidthD:"Heel Width DL",
//   jointWidth:"Join Width",
//   jointWidthD:"Join Width DL",
//   instep1Height:"Instep 1 Height",
//   instep2Height:"Instep 2 Height",
//   metaHeight:"Meta Height",
//   archHeight:"Arch Height",
//   toeHeight:"Toe Height",
//   metaExtPoint:"Meta Ext Point",
//   metaIntPoint:"Meta Int Point",
//   heelExtPoint:"Heel Ext Point",
//   heelIntPoint:"Heel Int Point",
//   morphoGroundPoint:"Morpho Ground Point",
//   halluxValgusAngle:"Hallux Valgus Angle",
//   lastPrint:"Last Print",
//   arch:"Arch",
// }
