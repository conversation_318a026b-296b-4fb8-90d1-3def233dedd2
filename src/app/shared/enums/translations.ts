export const LastInfosTranslationKey: Record<string, string> = {
    Greek: "BACKEND.Greek",
    Egyptian: "BACKEND.Egyptian",
    Roman: "BACKEND.Roman",
    German: "BACKEND.German",
    Celtic: "BACKEND.Celtic",
    Flat: "BACKEND.Flat",
    Normal: "BACKEND.Normal",
    High: "BACKEND.High",
    Male: "BACKEND.Male",
    Female: "BACKEND.Female",
    Unknown: "BACKEND.Unknown",
    Kid: "BACKEND.Kid",
    Adult: "BACKEND.Adult",
    "L / R": "BACKEND.L / R",
    L: "BACKEND.L",
    R: "BACKEND.R",
    Mixed: 'COMMON.MIXED',
    Multiple: 'COMMON.MULTIPLE'
}

export const LastMeasuresTranslationKey: Record<string, string> = {
    "Navicular Perimeter": "BACKEND.Navicular Perimeter",
    "Last Length": "BACKEND.Last Length",
    "Entry Perimeter": "BACKEND.Entry Perimeter",
    "Joint Perimeter": "BACKEND.Joint Perimeter",
    "Toe Perimeter": "BACKEND.Toe Perimeter",
    "Heel Width": "BACKEND.Heel Width",
    "Heel Width DL": "BACKEND.Heel Width DL",
    "Join Width": "BACKEND.Join Width",
    "Join Width DL": "BACKEND.Join Width DL",
    "Instep 1 Height": "BACKEND.Instep 1 Height",
    "Instep 2 Height": "BACKEND.Instep 2 Height",
    "Meta Height": "BACKEND.Meta Height",
    "Heel Height": "BACKEND.Heel Height",
    "Arch Height": "BACKEND.Arch Height",
    "Toe Height": "BACKEND.Toe Height",
    "Meta Ext Point": "BACKEND.Meta Ext Point",
    "Meta Int Point": "BACKEND.Meta Int Point",
    "Heel Ext Point": "BACKEND.Heel Ext Point",
    "Heel Int Point": "BACKEND.Heel Int Point",
    "Morpho Ground Point": "BACKEND.Morpho Ground Point",
    "Hallux Valgus Angle": "BACKEND.Hallux Valgus Angle",
    "Last Print": "BACKEND.Last Print",
    "Last Type": "BACKEND.Last Type",
    "Arch": "BACKEND.Arch",
    "Ground": "BACKEND.Ground"
}
