export const GENDER_LIST = [
    { name: "Male", slug: "male" },
    { name: "Female", slug: "female" }

]

export const CATEGORY_LIST = [
    { name: "Kid", slug: "kid" },
    { name: "Adult", slug: "adult" },
]
export const SYSTEM_SIZES_LIST = [
    { name: "EU", slug: "eu" },
    { name: "US", slug: "us" },
    { name: "UK", slug: "uk" },
]
export const HEIGHT_LIST = [
    { name: "None", slug: "none" },
    { name: "He<PERSON>", slug: "heel" },
    { name: "<PERSON>", slug: "arch" },
]
export const FERRATO_TYPE_LIST = [
    { name: "None", slug: "none" },
    { name: "He<PERSON>", slug: "heel" },
    { name: "Cava", slug: "cava" },
    { name: "Full Bottom", slug: "full_bottom" },
    { name: "Tip", slug: "tip" },
]
export const BOTTOM_LOCK_OPTION_LIST = [
    { name: "None", slug: "none" },
    { name: "He<PERSON>", slug: "heel" },
    { name: "Waist", slug: "waist" },
    { name: "Full Bottom", slug: "full_bottom" }
]
export const TOE_SHAPE_TYPE_LIST = [
    { name: "Curved", slug: "curved" },
    { name: "Square", slug: "square" },
    { name: "Pointed", slug: "pointed" },
    { name: "Curved Square", slug: "curved_square" },
    { name: "Assymmedtrical", slug: "assymmedtrical" }
]

export const TYPE_FIELDS_LIST = [
    { name: "Text", slug: "text", translationKey: 'TEXT_TYPE' },
    { name: "Number", slug: "number", translationKey: 'NUMBER_TYPE' },
    { name: "Choice", slug: "choice", translationKey: 'CHOICE_TYPE' }

]

export const YES_NO_LIST = [
    { name: "Yes", slug: "yes", value: 1, translationKey: 'COMMON.YES' },
    { name: "No", slug: "no", value: 0, translationKey: 'COMMON.NO' }

]

export const SCANNER_TYPES = [
    { name: "Blue Scan", slug: "blue-scan" },
    { name: "Mobile", slug: "mobile" },
    { name: "Fixed", slug: "fixed" },
    { name: "Other", slug: "other" }
]
