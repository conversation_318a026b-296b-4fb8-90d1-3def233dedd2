import { AbstractControl } from '@angular/forms';

export function ValidatePassword(control: AbstractControl) {
    const password = control.value as string;

    if (!password) return null; // If the password is empty, skip validation

    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isValidLength = password.length >= 10;

    const passwordValid =
      hasUpperCase && hasLowerCase && hasNumber && hasSpecialChar && isValidLength;

    return passwordValid ? null : { complex_password: true };
}