
<div class="{{width}} field-container position-relative">
    <div>
        <p class="name" [ngClass]="{capitalize: capitalize}">{{name | translate}}</p>
        <div>
            <span class="help" attr.data-help="{{ help }}" *ngIf="help != ''"></span>
            <span class="required" attr.data-help="{{'Required Field'}}" *ngIf="required"></span>

            <!-- <img class="help" attr.data-help="{{'dsdsdsd'}}">
            <img class="required" attr.data-help="{{'Required Field'}}" *ngIf="required" src="assets/icons/required-icon.svg" > -->
        </div>

    </div>
    <div >
        <ng-content></ng-content>
    </div>
    <div *ngIf="disabled" class="disabled-field"></div>
</div>
<div *ngIf="unit_width != ''" class="{{unit_width}} px-3">
    <ng-content select="[unit]"></ng-content>
</div>