<div class="d-flex flex-column h-100" *ngIf="selected_last">
    <div class="d-flex align-items-center justify-content-between w-100 px-4">
        <div class="title">
            <img src="assets/icons/report-icon.svg" alt="report icon">
            <h3>{{ 'Last View' | translate }}</h3>
        </div>
        <div class="d-flex align-items-center" *ngIf="isProcessed()">
            <!-- <button class="btn-icon" (click)="showDashboard()" *ngIf="showDataIcon()">
                <img src="assets/icons/show-dashboard.svg" alt="show dashboard">
            </button> -->
            <button class="btn-icon" (click)="editLast()">
                <img src="assets/icons/edit-icon.svg" alt="edit icon">
            </button>
            <button class="btn-icon" (click)="downloadPDF()">
                <img src="assets/icons/download-pdf-icon.svg" alt="download pdf icon">
            </button>
            <button class="btn-icon" (click)="download()">
                <img src="assets/icons/download-stl-icon.svg" alt="download stl icon">
            </button>
        </div>
    </div>
    <div class="scene-container" style="height: 40vh;">
        <img src="assets/icons/full-screen-icon.svg" alt="full-screen-icon" class="full-screen-icon"
            (click)="onFullScreen()">
        <app-main-scene [zoom]="zoom"></app-main-scene>

    </div>
    <div class="flex-grow-1" style="height: 0;" *ngIf="isProcessed()">
        <app-measurements-table 
            [selectedLast]="selected_last" 
            [measures]="mesures"
        ></app-measurements-table>
    </div>
    <div *ngIf="!isProcessed()" class="d-flex align-items-center justify-content-center">
        <h1 *ngIf="!isError()">{{ 'Processing' | translate }}</h1>
        <h1 *ngIf="isError()">{{ 'Error' | translate }}</h1>
    </div>
</div>

<app-full-screen [showAll]="showAll"   [selected_last]="selected_last" *ngIf="is_full_screen" ></app-full-screen>

