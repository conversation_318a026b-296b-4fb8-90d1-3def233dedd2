import { Component, EventEmitter, inject, Input, OnC<PERSON>es, OnDestroy, OnInit, Output, signal } from '@angular/core';
import { Subscription } from 'rxjs';
import { SceneService } from 'src/app/scene/main/scene.service';
import { FileService } from 'src/app/services/file.service';
import { SpinnerService } from '../spinner/spinner.service';
import { LocalService } from 'src/app/services/local.service';
import { MessagesService } from '../messages/messages.service';
import { CurvesService } from 'src/app/services/curves.service';
import { Router } from '@angular/router';
import { TitleCasePipe } from '@angular/common';
import { LastInfosTranslationKey } from '../enums/translations';
import { ColumnsTranslationKey } from 'src/app/enums/columns-translation-key';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-last-view',
  templateUrl: './last-view.component.html',
  styleUrls: ['./last-view.component.scss'],
  providers: [TitleCasePipe]
})
export class LastViewComponent implements OnInit, OnDestroy, OnChanges {
  @Input() selected_last: any = null;
  @Output() showDashboardEvent = new EventEmitter<boolean>(false);
  zoom = 1
  subscriptions: Subscription[] = []
  mesures: any[] = [];
  formated_keys: string[] = [];
  average_filter: any[] = [];
  is_full_screen:boolean = false;
  // Enum
  lastInfosTranslations = LastInfosTranslationKey;
  columnsTranslationKey = ColumnsTranslationKey;
  // Signals
  groups = signal<any[]>([]);
  filters = signal<any[]>([]);
  filterValues = signal<any[]>([]);
  filterValues2 = signal<any[]>([]);

  constructor(
    private SceneService: SceneService,
    private StlService: FileService,
    private router: Router,
    private CurvesService: CurvesService,
    private MessagesService: MessagesService,
    private LocalService: LocalService, 
    private SpinnerService: SpinnerService,
    private titlecasePipe:TitleCasePipe,
    private translateService:TranslateService
  ) { }

  ngOnChanges(changes: any): void {
    if (changes.selected_last) {
      this.filters.set(this.filterkeys());
      this.filterValues.set(this.filters().map(f => this.getFilterValue(f[0])));
      this.filterValues2.set(this.filters().map(f => this.getFilterValue(f[1])));
      this.getObjectToScene();
    }
  }

  ngOnInit(): void {
    this.subscriptions.push(this.LocalService.open_fullscreen.subscribe((isVisible) => {
      this.is_full_screen = isVisible
      console.log('full screen');
    }))
    this.subscriptions.push(this.LocalService.selected_last_data_arrived.subscribe((data) => {
      this.mesures = Object.values(data.mesures).filter((el:any)=>el.type != 'group');
    }))
    this.subscriptions.push(this.CurvesService.curve_status.subscribe(({ slug, status}) => {
        let index = this.mesures.findIndex((mesure) => mesure.slug == slug);
        if (index != -1) {
          this.mesures[index].show = status
        }
    }))
  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
  }
  toggle(slug: string) {
    this.CurvesService.toggleMesure.next({ slug });
  }
  toggleAll() {
    const status = this.showAll;
    this.groups().forEach((mesure: any) => {
      if (mesure.show == status) {
          this.toggle(mesure.slug);
      }
    });
  }
  download() {
    this.SpinnerService.show(1);
    this.subscriptions.push(this.StlService.getDownloadStl(this.selected_last.id).subscribe({
      next: (data) => {
        const name = this.selected_last.reference + '_' + '.stl';
        this.StlService.downloadFile(data, name)
        this.MessagesService.set(this.translateService.instant('Downloaded Successfully'), 'green')
        this.SpinnerService.hide(1);
      },
      error: (err: any) => {
        this.SpinnerService.hide(1);
      },
    }))
  }
  downloadPDF() {
    this.SpinnerService.show(1);
    const data = { image: this.CurvesService.image }
    this.subscriptions.push(this.StlService.getDownloadPdf(this.selected_last.id, 'test',data).subscribe({
      next: (data) => {
        const name = this.selected_last.reference + '_' + '.pdf';
        this.StlService.downloadFile(data, name)
        this.MessagesService.set('PDF ' + this.translateService.instant('Downloaded Successfully'), 'green')
        this.SpinnerService.hide(1);
      },
      error: (err: any) => {
        this.SpinnerService.hide(1);
      },
    }))
  }

  isProcessed() {
    return this.selected_last.status == 'processed'
  }
  isError() {
    return this.selected_last.status == 'error'
  }
  getObjectToScene() {
    if (this.isProcessed()) {
      this.SpinnerService.show(1);
      this.SceneService.download_stl.next({ ref: this.selected_last.ref, id: this.selected_last.id })
    } else {
      this.SceneService.clear_scene.next();
      this.SpinnerService.hide(1);
    }

  }
  filterkeys() {
    if (this.selected_last.filter instanceof Object) {
      const keys = Object.keys(this.selected_last.filter)
      let keys_formated = [];
      const chunkSize = 2;
      for (let i = 0; i < keys.length; i += chunkSize) {
        const chunk = keys.slice(i, i + chunkSize);
        keys_formated.push(chunk)
      }
      return keys_formated;
    } else {
      return []
    }
  }
  getFilterValue(filter: any) {
    if (filter instanceof Object) {
      if (Array.isArray(filter) && filter.length == 1) {
        return this.titlecasePipe.transform(filter[0]);
      }
      return 'Multiple';
    } else if(typeof filter === 'string'){
      return this.titlecasePipe.transform(filter.split('_').join(' '));
    } else {
      return filter;
    }
  }

  editLast() {
    this.router.navigate(["last-inventory/edit", this.selected_last.ref])
  }

  onFullScreen() {
    this.LocalService.open_fullscreen.next(true);
  }

  showDashboard() {
    this.router.navigate(["last-averaging/new"], {
      queryParams: {
        'last-avg-ref': this.selected_last.ref
      }
    });
    this.showDashboardEvent.emit(true);
  }
  
  get showAll(){
    return this.mesures.findIndex((measure) => measure.show) != -1
  }
}
