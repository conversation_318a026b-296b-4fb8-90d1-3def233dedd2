import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from './spinner/spinner.component';
import { SearchableSelectComponent } from './searchable-select/searchable-select.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NewComponent } from './new/new.component';
import { FieldComponent } from './ui/field/field.component';
import { SceneModule } from '../scene/scene.module';
import { HttpClientModule } from '@angular/common/http';
import { MessagesComponent } from './messages/messages.component';
import { LastViewComponent } from './last-view/last-view.component';
import { EmptyPipe } from '../pipes/empty/empty.pipe';
import { SpinnerHtmlComponent } from './spinner/spinner-html.component';
import { RouterModule } from '@angular/router';
import { NotFoundComponent } from '../pages/not-found/not-found.component';
import { LayoutsModule } from '../layouts/layouts.module';
import { HomeComponent } from '../pages/home/<USER>';
import { LoginComponent } from '../pages/login/login.component';
import { LoginFormComponent } from '../pages/login/login-form/login-form.component';
import { RecaptchaFormsModule, RecaptchaModule } from 'ng-recaptcha';
import { SearchComponent } from './search/search.component';
import { SearchFormComponent } from './search/search-form/form-search.component';
import { TranslateModule } from '@ngx-translate/core';
import { PopupComponent } from './ui/popup/popup.component';
import { SliderComponent } from './slider/slider.component';
import { TableCustomComponent } from './table-custom/table-custom.component';
import { FullScreenComponent } from './full-screen/full-screen.component';
import { ObjectToArrayPipe } from '../pipes/enum-to-array.pipe';
import { MeasurementsTableComponent } from './measurements-table/measurements-table.component';
import { DashSeparatorPipe } from '../pipes/dash-separator.pipe';
import { IsNumberPipe } from '../pipes/is-number.pipe';
import { CapitalizePipe } from '../pipes/capitilize.pipe';
import { CustomTranslatePipe } from '../pipes/custom-translation.pipe';
import { ImageUploadComponent } from './image-upload/image-upload.component';
import { SupportComponent } from '../admin/support/support.component';
import { NotificationComponent } from './notification/notification.component';
import { ResetPasswordComponent } from '../pages/reset-password/reset-password.component';
import { StandardComponent } from '../admin/standard/standard.component';
// import { ResetPasswordRequestComponent } from '../pages/reset-password/reset-password-request.component';



@NgModule({
  declarations: [
    SpinnerComponent,
    SearchableSelectComponent,
    NewComponent,
    FieldComponent,
    MessagesComponent,
    LastViewComponent,
    MeasurementsTableComponent,
    EmptyPipe,
    ObjectToArrayPipe,
    SpinnerHtmlComponent,
    NotFoundComponent,
    HomeComponent,
    LoginComponent,
    LoginFormComponent,
    SearchComponent,
    SearchFormComponent,
    PopupComponent,
    SliderComponent,
    TableCustomComponent,
    FullScreenComponent,
    ImageUploadComponent,
    SupportComponent,
    NotificationComponent,
    DashSeparatorPipe,
    IsNumberPipe,
    CapitalizePipe,
    CustomTranslatePipe,
    ResetPasswordComponent,
    StandardComponent
    // ResetPasswordRequestComponent
  ],
  imports: [
    ReactiveFormsModule,
    HttpClientModule,
    CommonModule,
    FormsModule,
    SceneModule,
    RouterModule,
    LayoutsModule,
    RecaptchaModule,
    RecaptchaFormsModule,
    TranslateModule
  ],
  exports: [
    SpinnerComponent,
    SearchableSelectComponent,
    NewComponent,
    FieldComponent,
    MessagesComponent,
    LastViewComponent,
    MeasurementsTableComponent,
    EmptyPipe,
    SpinnerHtmlComponent,
    SearchComponent,
    SearchFormComponent,
    PopupComponent,
    SliderComponent,
    TableCustomComponent,
    FullScreenComponent,
    ImageUploadComponent,
    SupportComponent,
    NotificationComponent,
    ObjectToArrayPipe,
    DashSeparatorPipe,
    IsNumberPipe,
    TranslateModule,
    CapitalizePipe,
    CustomTranslatePipe,
    StandardComponent
  ]
})
export class SharedModule { }
