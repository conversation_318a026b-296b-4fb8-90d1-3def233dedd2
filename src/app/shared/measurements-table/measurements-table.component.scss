@import "../../../styles/variables";

.metrics-table {
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding-top: 0.7rem;
    flex-grow: 1;
    height: 0;

    &>h4 {
        background-color: $dark;
        color: white;
        font-size: 1rem;
        padding: 0.4rem 0 0.3rem 0;
        margin: 0;
        width: 100%;
        text-align: center;
        border-radius: 0.5rem 0.5rem 0 0;
    }

    &>div {
        table {
            &>tbody>tr:nth-child(2n) {
                background-color: rgba($dark , 0.04);
            }

            &>tbody>tr>td {
                position: relative;
                vertical-align: middle;
                line-height: 1.3rem;

                &.bordered {
                    border-right: 1px solid $dark;
                }

                &>span {
                    display: inline-block;
                    padding: 0.1rem;
                    font-size: 0.8rem;
                    text-transform: capitalize;

                    &.group-name {
                        padding-left: 0.1rem;

                        &>b {
                            font-size: 0.9rem;
                        }
                    }

                    &:last-child {
                        text-align: end;
                        padding-right: 1rem;
                        font-weight: 700;
                    }

                    &:first-child {
                        text-align: center;

                        &>img {
                            max-width: 1.25rem;
                            margin: auto;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
}

.test {
    display: flex;
    flex-wrap: wrap;
}

.all-metrics-icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: absolute;
    width: 15%;

    img {
        cursor: pointer;
    }
}
.all-measures{
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    max-height: 200px;
    .measure-row{
        width: 50%;
        height: 1.5rem;
        display: flex;
        flex-direction: row;
        &>span {
            display: inline-block;
            padding: 0.1rem 0.1rem 0.1rem 1.5rem;
            font-size: 0.8rem;
            font-weight: 700;
        }
        img{
            width:1.5rem;
        }
    }
}

.group-label {
    font-family: 'MullerBold';
    font-size: 0.9rem !important;
    line-height: 14.4px;
    transform: translateX(-10px);
}