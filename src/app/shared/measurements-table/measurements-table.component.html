<div class="w-100 h-100 px-2 pb-2 metrics-table">
    <h4 class="w-100">
        <div class="all-metrics-icon">
            <img 
                width="16px" 
                src="assets/icons/{{ showAll ? 'eye-icon-white.svg': 'close-eye-icon-white.svg' }}" 
                alt="eye icon"
                (click)="toggleAll()">
            <span style="font-size: 1rem;">{{ 'All' | translate }}</span>
        </div>
        {{ 'Metrics' | translate }}
    </h4>
    <div class="w-100 flex-grow-1" style="overflow-y:auto;">
        <table class="w-100">
            <tbody class="w-100 all-measures">
                <ng-container *ngFor="let mesure of groups(); let i = index">
                    <tr class="w-50 test">
                        <!-- <td class="w-100" [ngClass]="{'bordered': i < 16}"> -->
                        <td class="w-100 bordered">
                            <span class="w-15"><img *ngIf="mesure.mesure" (click)="toggle(mesure.slug)"
                                    src="assets/icons/{{ mesure.show ? '':'close-' }}eye-icon.svg"
                                    alt="eye icon"></span>
                            <span 
                                [ngClass]="{'w-55': !mesure.label, 'group-label': mesure.label}">
                                {{ (mesure.label ? mesure.label : mesure.name ) | translate }}
                            </span>
                            <span class="w-30" *ngIf="mesure.data | isNumber">{{ mesure.data }}</span>
                            <span 
                                class="w-30" 
                                *ngIf="!(mesure.data | isNumber)">{{ mesure.data | translate }}
                            </span>
                        </td>
                    </tr>
                </ng-container>
            </tbody>
        </table>
    </div>
</div>
