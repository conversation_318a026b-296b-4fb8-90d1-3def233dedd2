import { Component, Input, OnDestroy, OnInit, signal } from '@angular/core';
import { Subscription } from 'rxjs';
import { LocalService } from 'src/app/services/local.service';
import { CurvesService } from 'src/app/services/curves.service';
import { Router } from '@angular/router';
import { LastInfosTranslationKey, LastMeasuresTranslationKey } from '../enums/translations';

@Component({
    selector: 'app-measurements-table',
    templateUrl: './measurements-table.component.html',
    styleUrls: ['./measurements-table.component.scss']
})
export class MeasurementsTableComponent implements OnInit, OnDestroy {
    @Input() selectedLast: any = null;
    @Input() measures: any[] = [];

    subscriptions: Subscription[] = []
    // Signals
    groups = signal<any[]>([]);

    constructor(
        private curvesService: CurvesService,
        private router: Router,
        private localService: LocalService) { }

    ngOnInit(): void {
        this.subscriptions.push(this.localService.selected_last_data_arrived.subscribe((data) => {
            console.log("data", data)
            this.measures = Object.values(data.mesures).filter((el: any) => el.type != 'group');
            const groups: any[] = [];
            const types: string[] = [];

            this.measures.forEach(({ type, name }) => {
                if (types.indexOf(type) == -1) {
                    types.push(type);
                }
            });

            types.map(type => {
                groups.push({ label: this.curvesService.getMeasuresGroupKey(type) });
                this.measures.forEach(group => {
                    if (group.type === type) {
                        groups.push(group);
                    }
                });
            });
            this.groups.set(groups);
        }));

        this.subscriptions.push(this.curvesService.curve_status.subscribe(({ slug, status }) => {
            let index = this.measures.findIndex(mesure => mesure.slug == slug);
            if (index != -1) {
                this.measures[index].show = status;
            }
            this.groups.update(prevGroups => {
                return prevGroups.map(g => g.slug == slug ? { ...g, show: status } : { ...g })
            });
        }));
    }

    toggle(slug: string) {
        this.curvesService.toggleMesure.next({ slug });
    }

    toggleAll() {
        const status = this.showAll;
        this.groups().forEach((mesure: any) => {
            if (mesure.show == status) {
                this.toggle(mesure.slug);
            }
        });
    };

    get showAll() {
        return this.measures.findIndex((measure) => measure.show) != -1;
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(subscription => subscription.unsubscribe());
    }
}
