@import "../../../styles/variables";

.spinner {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: white;
    z-index: 20;
    .spinner-container {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 10%;
        min-width: 80px;

        svg {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40%;
            height: 40%;
            transform: translate(-50%, -50%);
        }

        .main {
            box-shadow: 0px 0px 10px rgba($primary, 0.25);
            position: relative;
            width: 100%;
            aspect-ratio: 1 / 1;
            // padding-top: calc(100% - 1rem);
            border: 0.5rem solid rgba($dark, 0.3);
            border-top: 0.5rem solid $primary;
            border-radius: 50%;
            animation: rotateAnimation 2s linear infinite;
        }
    }

    @keyframes rotateAnimation {
        0% {
            transform: rotate(-0deg);
        }

        100% {
            transform: rotate(-360deg);
        }
    }
}