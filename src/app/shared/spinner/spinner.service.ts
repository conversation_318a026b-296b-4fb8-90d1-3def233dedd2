import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SpinnerService {

  public spinnerSubject: Subject<{status: boolean,spinner_number:number }> = new Subject<{status: boolean,spinner_number:number }>();
  
  public globalSpinnerSubject: Subject<boolean> = new Subject<boolean>();

  public fitting_spinner: Subject<{status: boolean, type: 'report' | 'scene' | 'both' }> = new Subject<{status: boolean, type: 'report' | 'scene' | 'both' }>();

  constructor() { }

  show(spinner_number:number = 0) {
    this.spinnerSubject.next({spinner_number: spinner_number, status:true})
  }
  
  hide(spinner_number:number = 0) {
    this.spinnerSubject.next({spinner_number: spinner_number, status:false})
  }

  show_fitting(type: 'report' | 'scene' | 'both' = 'both') {
    this.fitting_spinner.next({type: type, status:true})
  }
  
  hide_fitting(type: 'report' | 'scene' | 'both' = 'both') {
    this.fitting_spinner.next({type: type, status:false})
  }
}
