import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { MessagesService } from '../messages/messages.service';

@Component({
  selector: 'app-image-upload',
  templateUrl: './image-upload.component.html',
  styleUrls: ['./image-upload.component.scss']
})
export class ImageUploadComponent {
  files: File[] = [];
  @Input() imagePreviews: { url: string | ArrayBuffer | null; progress?: number }[] = [];
  @Input() addAttachmentsBtnEnabled: boolean;
  @Output() uploadImages = new EventEmitter();
  // Services
  alert = inject(MessagesService);


  onFilesSelected(event: any): void {
    const selectedFiles = Array.from(event.target.files) as File[];

    selectedFiles.forEach(file => {
      if (this.isImage(file)) {
        this.files.push(file);
        this.readImageFile(file);
      } else {
        this.alert.set('Only image files are allowed!', 'error');
      }
    });

    this.uploadImages.emit(this.imagePreviews);
  }

  private isImage(file: File): boolean {
    const fileType = file.type.split('/')[0];
    return fileType === 'image';
  }

  private readImageFile(file: File): void {
    const reader = new FileReader();
    const imagePreview = { url: null, progress: 0 };

    reader.onprogress = (e: ProgressEvent) => {
      if (e.lengthComputable) {
        imagePreview.progress = Math.round((100 * e.loaded) / e.total);
      }
    };

    reader.onload = (e: any) => {
      imagePreview.url = e.target.result;  // Store the image as base64 string
    };

    this.imagePreviews.push(imagePreview);

    reader.readAsDataURL(file);
  }
}
