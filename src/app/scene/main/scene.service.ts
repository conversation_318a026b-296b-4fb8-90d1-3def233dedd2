import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class SceneService {

    show_stl:Subject<any> = new Subject<any>();

    converted_file:Subject<{file: File, direction : 'left' | 'right' | 'stl'}> = new Subject<{file: File, direction : 'left' | 'right' | 'stl'}>();

    show_second_stl:Subject<any> = new Subject<any>();

    download_stl:Subject<any> = new Subject<any>();

    download_second_stl:Subject<any> = new Subject<any>();

    // last_direction:Subject<string> = new Subject<string>();

    clear_scene:Subject<void> = new Subject<void>();

    resize:Subject<void> = new Subject<void>();
    
    capture:Subject<void> = new Subject<void>();

  constructor(
  ) { }
}