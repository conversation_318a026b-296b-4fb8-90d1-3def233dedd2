import { AfterViewInit, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import * as THREE from 'three';
import { ArcballControls } from 'three/examples/jsm/controls/ArcballControls';
import { SceneService } from './scene.service';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { STLExporter } from 'three/examples/jsm/exporters/STLExporter';
import { environment } from 'src/environments/environment';
import { SpinnerService } from 'src/app/shared/spinner/spinner.service';
import { Subscription } from 'rxjs';
import { FileService } from 'src/app/services/file.service';
import { LocalService } from 'src/app/services/local.service';

import { Line2 } from 'three/examples/jsm/lines/Line2';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';
import { CurvesService } from 'src/app/services/curves.service';
import { TranslateService } from '@ngx-translate/core';
import { MessagesService } from 'src/app/shared/messages/messages.service';
@Component({
  selector: 'app-main-scene',
  templateUrl: './scene.component.html',
  styleUrls: ['./scene.component.scss']
})
export class MainSceneComponent implements OnInit, AfterViewInit, OnDestroy {

  subscriptions: Subscription[] = []

  @ViewChild('canvascontainer') private canvasContainerRef: ElementRef;
  private camera!: THREE.OrthographicCamera;
  private renderer!: THREE.WebGLRenderer;
  private scene!: THREE.Scene;
  private controls!: ArcballControls;
  private meshGroup!: THREE.Group;
  private mesuresGroup!: THREE.Group;
  private size: { width: number, height: number };

  @Input() zoom: number = 1
  private material: THREE.Material = new THREE.MeshStandardMaterial({
    color: "#929292",
    depthTest: true,
    depthWrite: true,
    metalness: 0.35,
    roughness: 0.85,
    opacity: 0.85,
    transparent: true,
    flatShading: false,
    emissive: '#000000',
    vertexColors: true
  });
  // private last_material: THREE.Material = new THREE.MeshStandardMaterial({
  //   color: "#0f90f6",
  //   depthTest: true,
  //   depthWrite: true,
  //   metalness: 0.35,
  //   roughness: 0.85,
  //   opacity: 0.85,
  //   transparent: true,
  //   flatShading: false,
  //   emissive: '#000000',
  //   vertexColors: true
  // });


  get canvasContainer() {
    return this.canvasContainerRef.nativeElement
  }

  constructor(
    private SceneService: SceneService,
    private MessagesService: MessagesService,
    private StlService: FileService,
    private TranslateService: TranslateService,
    private CurvesService: CurvesService,
    private LocalService: LocalService,
    private SpinnerService: SpinnerService) {

  }
  ngOnInit(): void {
    console.log('Scene initied')

    this.subscriptions.push(this.CurvesService.toggleMesure.subscribe((data) => {
      this.toggle(data);
    }))

    this.subscriptions.push(this.SceneService.clear_scene.subscribe(() => {
      this.meshGroup.clear();
      this.mesuresGroup.clear();
    }))
    this.subscriptions.push(this.SceneService.download_stl.subscribe((emited_data) => {
      console.log(emited_data)
      let data = null;
        data = 'api/' + this.lang + '/lasts/' + emited_data.id;
      this.meshGroup.clear();
      this.mesuresGroup.clear();
      this.controls.reset()

      const loader = new STLLoader()
      const token = localStorage.getItem('token');
      loader.setRequestHeader({ Authorization: 'Bearer ' + token })
      if (data != null) {
        loader.load(environment.api_url + data + '.stl?' + new Date().getTime().toString(), (geometry) => {
          let count = geometry.getAttribute('position').count
          let colors = new Float32Array(count * 3);
          colors.fill(1);
          geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
          const mesh = new THREE.Mesh(geometry, this.material)
          mesh.name = 'last'
          this.meshGroup.clear();
          this.mesuresGroup.clear();
          this.meshGroup.add(mesh);
          if (emited_data.isEdit) {
            this.camera.position.set(1, -500, 50);
            this.camera.zoom = 1;
            this.camera.updateProjectionMatrix();
            this.SpinnerService.hide(1);
          } else {
            this.subscriptions.push(this.StlService.getStlMesures(emited_data.id).subscribe({
              next: (value: any) => {
                console.log(value)
                this.create_curves(value.data.mesures);
                this.LocalService.addData(value.data)
                this.CurvesService.image = this.takeLastImage(geometry)
                this.SpinnerService.hide(1);
              },
              error: (error) => {
                this.SpinnerService.hide(1);
              },
            }))

          }
        },
          (xhr) => {
            // console.log(Math.round((xhr.loaded / xhr.total)* 100) + '% loaded')
          },
          (error: any) => {
            this.SpinnerService.hide(1);
          }
        )
      }
    }))
    this.subscriptions.push(this.SceneService.show_stl.subscribe((data) => {
      let reader = new FileReader();
      reader.addEventListener('load', (event: any) => {
        try {
          let contents = event.target.result;
          let geometry: any = null
          if (data.extension == 'stl') {
            geometry = new STLLoader().parse(contents);
            if (geometry != null) {
              geometry.sourceType = "stl";
              geometry.sourceFile = data.file.name;
            }
          }else if (data.extension == 'obj') {
            const object: any = new OBJLoader().parse(contents);
            geometry = object.children[0].geometry
            if (geometry != null) {
              geometry.sourceType = "obj";
              geometry.sourceFile = data.file.name;
            }
          }
            let count = geometry.getAttribute('position').count
            let colors = new Float32Array(count * 3);
            colors.fill(1);
            geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

            geometry.computeBoundingBox()
            const width_height_depth = new THREE.Vector3();
            geometry.boundingBox?.getSize(width_height_depth)
            if (width_height_depth.x < 1 && width_height_depth.y < 1 && width_height_depth.z < 1) {
              geometry.scale(1000, 1000, 1000);
            }
            let mesh = new THREE.Mesh(geometry, this.material);
            mesh.name = 'last';
            this.meshGroup.clear();
            this.mesuresGroup.clear();
            this.meshGroup.add(mesh);

            const exporter = new STLExporter();
            const stlString = exporter.parse(mesh, { binary: false });
            const blob = new Blob([stlString], { type: 'application/octet-stream' });
            const file =  new File([blob], data.file.name, { type: 'application/octet-stream' });
            this.SceneService.converted_file.next({file: file, direction : 'left'})
        } catch (error) {
          console.error(error)
          this.MessagesService.set('Error load file', 'error')
        }
      }, false);
      if (reader.readAsBinaryString !== undefined) {
        reader.readAsBinaryString(data.file);
      } else {
        reader.readAsArrayBuffer(data.file);
      }
    }))

    this.subscriptions.push(this.SceneService.resize.subscribe(() => {
      this.resize();
    }))
    this.subscriptions.push(this.SceneService.capture.subscribe(() => {
      this.capture();
    }))

  }
  ngOnDestroy(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe()
    }
    this.scene.clear();
  }

  ngAfterViewInit(): void {
    this.size = {
      width: this.canvasContainer.clientWidth,
      height: this.canvasContainer.clientHeight
    }
    this.createScene();
    this.startRenderingLoop();

    setTimeout(() => {
      this.resize()
    })
  }

  private createScene() {

    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0xfcfcfc)

    let near = 0.1;
    let far = 3000;

    this.camera = new THREE.OrthographicCamera(
      this.size.width / -2,
      this.size.width / 2,
      this.size.height / 2,
      this.size.height / -2,
      near,
      far);

    this.camera.position.set(0, -500, 200);
    this.camera.zoom = this.zoom;
    this.camera.updateProjectionMatrix();
    this.addLights();
    this.meshGroup = new THREE.Group();
    this.meshGroup.name = 'meshs';
    this.mesuresGroup = new THREE.Group();
    this.mesuresGroup.name = 'mesures';
    this.scene.add(this.meshGroup);
    this.scene.add(this.mesuresGroup);

  }

  private startRenderingLoop() {
    this.renderer = new THREE.WebGLRenderer();
    this.renderer.setPixelRatio(devicePixelRatio);
    this.renderer.setSize(this.size.width, this.size.height);
    this.canvasContainer.appendChild(this.renderer.domElement);
    this.controls = new ArcballControls(this.camera, this.renderer.domElement, this.scene);
    this.controls.setGizmosVisible(false);
    this.controls.activateGizmos(false);
    this.controls.addEventListener('change', () => {
      this.renderer.render(this.scene, this.camera);
    });

    let component: MainSceneComponent = this;
    (function render() {
      requestAnimationFrame(render);
      component.renderer.render(component.scene, component.camera);
      component.controls.update();
    })();

  }

  addLights() {
    const lightGroup: THREE.Group = new THREE.Group();
    lightGroup.name = 'lightGroup';

    const AmbientLight = new THREE.AmbientLight(0x888888); // soft white light
    lightGroup.add(AmbientLight);

    const light1: THREE.SpotLight = new THREE.SpotLight(0x888888, 3);
    light1.position.set(0, 500, 0);
    light1.castShadow = true;
    light1.shadow.bias = -0.000222;
    light1.shadow.mapSize.width = 1024;
    light1.shadow.mapSize.height = 1024;
    lightGroup.add(light1);

    const light2: THREE.SpotLight = light1.clone();
    light2.position.set(0, -500, 0);
    lightGroup.add(light2);

    const light3: THREE.SpotLight = light1.clone();
    light3.position.set(0, 0, 500);
    lightGroup.add(light3);

    const light4: THREE.SpotLight = light1.clone();
    light4.position.set(0, 0, -500);
    lightGroup.add(light4);

    const light5: THREE.SpotLight = light1.clone();
    light5.position.set(500, 0, 0);
    lightGroup.add(light5);

    const light6: THREE.SpotLight = light1.clone();
    light6.position.set(-500, 0, 0);
    lightGroup.add(light6);

    this.scene.add(lightGroup)
  }

  create_curves(mesures_list: any) {
    const keys = Object.keys(mesures_list)
    this.mesuresGroup.clear();
    keys.forEach((key: any) => {
      let mesure = mesures_list[key]
      if(mesure){
      let color = mesure.color
      let geometry: any;
      let material: any;
      let mesh: any;
      let positions = [];
      if (mesure.mesure != null && mesure.type != "") {
        if (mesure.type == "Point") {
          geometry = new THREE.SphereGeometry(1.5, 9, 9);
          material = new THREE.MeshBasicMaterial({ color: color });
          let group: any = new THREE.Group();
          if (mesure.mesure.length > 3) {
            group.name = mesure.slug;
            group.visible = mesure.show;
          }

          for (let i = 0; i < mesure.mesure.length / 3; i++) {
            mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(mesure.mesure[i * 3], mesure.mesure[i * 3 + 1], mesure.mesure[i * 3 + 2])
            if (mesure.mesure.length > 3) {
              group.add(mesh);
            } else {
              mesh.name = mesure.slug;
              mesh.visible = mesure.show;
              this.mesuresGroup.add(mesh);
            }
          }
          if (mesure.mesure.length > 3) {
            group.visible = mesure.show
            this.mesuresGroup.add(group);
          }
        } else if (mesure.type == "Line" || mesure.type == "Angle" || mesure.type == "Width" || mesure.type == "Height") {
          for (let i = 0; i < mesure.mesure.length; i++) {
            positions.push(parseFloat(mesure.mesure[i][0]), parseFloat(mesure.mesure[i][1]), parseFloat(mesure.mesure[i][2]));
          }
          geometry = new LineGeometry();
          geometry.setPositions(positions);
          material = new LineMaterial({
            color: color,
            linewidth: 1,
            vertexColors: false,
            dashed: false,
            alphaToCoverage: false,
            worldUnits: true
          });
          mesh = new Line2(geometry, material);
          mesh.name = mesure.slug;
          mesh.visible = mesure.show
          this.mesuresGroup.add(mesh);

        } else if (mesure.type == "Curve") {
          for (let i = 0; i < mesure.mesure.length; i++) {
            positions.push(parseFloat(mesure.mesure[i][0]), parseFloat(mesure.mesure[i][1]), parseFloat(mesure.mesure[i][2]));
          }
          geometry = new LineGeometry();
          geometry.setPositions(positions);
          material = new LineMaterial({
            color: color,
            linewidth: 1,
            vertexColors: false,
            dashed: false,
            alphaToCoverage: false,
            worldUnits: true
          });
          mesh = new Line2(geometry, material);
          mesh.name = mesure.slug;
          mesh.visible = mesure.show
          this.mesuresGroup.add(mesh);
        } else if (mesure.type == "vertexGroup") {
          const vertices = new Float32Array(mesure.mesure);
          let bufferAttribute = new THREE.BufferAttribute(vertices, 3)
          geometry = new THREE.BufferGeometry();
          geometry.setAttribute('position', bufferAttribute);
          material = new THREE.PointsMaterial({ color: color, size: 3 });
          mesh = new THREE.Points(geometry, material);
          mesh.name = mesure.slug;
          mesh.visible = mesure.show;
          this.mesuresGroup.add(mesh);
        } else if (mesure.type == "ground") {
          this.create_ground(mesure.mesure, mesure.show)
        }
      }
    }
    });
  }

  toggle(data: { slug: string}) {
      let object = this.mesuresGroup.getObjectByName(data.slug);
    if (object) {
      object.visible = !object.visible
      this.CurvesService.curve_status.next({ slug: data.slug, status: object.visible })
    }
  }
  resize(width: number = 0, height: number = 0) {
    if (width == 0) {
      width = this.canvasContainer.clientWidth
    }
    if (height == 0) {
      height = this.canvasContainer.clientHeight
    }
    this.renderer.setSize(width, height);
    this.camera.left = width / -2
    this.camera.right = width / 2
    this.camera.top = height / 2
    this.camera.bottom = height / -2;
    this.camera.updateProjectionMatrix();
    this.controls.update();
  }
  capture(width: number = 0, height: number = 0, zoom: number = 1) {

    this.camera.zoom = zoom
    this.resize(width, height);
    this.renderer.render(this.scene, this.camera);
    let dataURL = this.renderer.domElement.toDataURL();
    this.camera.zoom = this.zoom
    this.resize();
    return dataURL;

  }
  takeLastImage(geometry: THREE.BufferGeometry) {
    geometry.computeBoundingBox();
    let box = geometry.boundingBox
    if (box != null) {
      let widthObject = (box.max.x - box.min.x) + 30;
      let heightObject = (box.max.z - box.min.z) + 30;
      let facteur = window.innerWidth / widthObject;
      const width = widthObject * facteur
      const height = heightObject * facteur
      return this.capture(width, height, facteur)
    }
    return null;
  }

  create_ground(ground: { length: number, width: number, depth: number }, visible: boolean = false) {
    const geometryGround = new THREE.BoxGeometry(ground.length, ground.width, 1);
    const materialGround = new THREE.MeshBasicMaterial(
      {
        color: '#79CBFD',
        opacity: 0.3,
        transparent: true
      });
    const groundMesh = new THREE.Mesh(geometryGround, materialGround);
    groundMesh.name = 'ground';
    groundMesh.visible = visible;
    groundMesh.position.z = ground.depth;
    this.mesuresGroup.add(groundMesh);
  }

  get lang() {
    return this.TranslateService.getDefaultLang() ?? 'en'
  }
}
